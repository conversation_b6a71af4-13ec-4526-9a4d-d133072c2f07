module ExamResultStatistics<PERSON><PERSON><PERSON>
  def process_html_content_with_question_title(docx, html_content, title_prefix)
    return [] if html_content.blank?

    base64_images = extract_base64_images(html_content)
    clean_html = remove_base64_images_from_html(html_content)
    parsed_content = parse_html_with_formatting(clean_html)

    docx.p do
      text title_prefix, bold: true, font: 'Times New Roman', size: 28

      if parsed_content.any? && parsed_content.first[:formatted_parts]
        parsed_content.first[:formatted_parts].each do |part|
          next if part[:text].blank?
          
          text_options = { font: 'Times New Roman', size: 28 }
          
          if part[:formatting][:bold]
            text_options[:bold] = true
          end
          
          if part[:formatting][:italic]
            text_options[:italic] = true
          end
          
          if part[:formatting][:underline]
            text_options[:underline] = true
          end

          if part[:formatting][:subscript]
            text "_#{part[:text]}", text_options
          elsif part[:formatting][:superscript]
            text "^#{part[:text]}", text_options
          else
            text part[:text], text_options
          end
        end
      else
        clean_text = clean_html_entities(clean_html)
        text clean_text, font: 'Times New Roman', size: 28 if clean_text.present?
      end
    end

    if parsed_content.length > 1
      parsed_content[1..-1].each do |element|
        if element[:type] == :paragraph && element[:formatted_parts]
          docx.p do
            element[:formatted_parts].each do |part|
              next if part[:text].blank?
              
              text_options = { font: 'Times New Roman', size: 28 }
              
              if part[:formatting][:bold]
                text_options[:bold] = true
              end
              
              if part[:formatting][:italic]
                text_options[:italic] = true
              end
              
              if part[:formatting][:underline]
                text_options[:underline] = true
              end

              if part[:formatting][:subscript]
                text "_#{part[:text]}", text_options
              elsif part[:formatting][:superscript]
                text "^#{part[:text]}", text_options
              else
                text part[:text], text_options
              end
            end
          end
        end
      end
    end

    base64_images.each do |image_path|
      add_image_safely(docx, image_path)
    end

    base64_images
  end

  def process_html_content_with_option_label(docx, html_content, label, is_correct = false)
    return [] if html_content.blank?

    base64_images = extract_base64_images(html_content)
    clean_html = remove_base64_images_from_html(html_content)
    parsed_content = parse_html_with_formatting(clean_html)

    docx.p do
      if is_correct
        text "#{label}. ", bold: true, color: '008000', font: 'Times New Roman', size: 28
      else
        text "#{label}. ", font: 'Times New Roman', size: 28
      end

      if parsed_content.any? && parsed_content.first[:formatted_parts]
        parsed_content.first[:formatted_parts].each do |part|
          next if part[:text].blank?
          
          text_options = { font: 'Times New Roman', size: 28 }
          
          if part[:formatting][:bold]
            text_options[:bold] = true
          end
          
          if part[:formatting][:italic]
            text_options[:italic] = true
          end
          
          if part[:formatting][:underline]
            text_options[:underline] = true
          end

          if part[:formatting][:subscript]
            text "_#{part[:text]}", text_options
          elsif part[:formatting][:superscript]
            text "^#{part[:text]}", text_options
          else
            text part[:text], text_options
          end
        end
      else
        clean_text = clean_html_entities(clean_html)
        text clean_text, font: 'Times New Roman', size: 28 if clean_text.present?
      end
    end

    if parsed_content.length > 1
      parsed_content[1..-1].each do |element|
        if element[:type] == :paragraph && element[:formatted_parts]
          docx.p do
            element[:formatted_parts].each do |part|
              next if part[:text].blank?
              
              text_options = { font: 'Times New Roman', size: 28 }
              
              if part[:formatting][:bold]
                text_options[:bold] = true
              end
              
              if part[:formatting][:italic]
                text_options[:italic] = true
              end
              
              if part[:formatting][:underline]
                text_options[:underline] = true
              end

              if part[:formatting][:subscript]
                text "_#{part[:text]}", text_options
              elsif part[:formatting][:superscript]
                text "^#{part[:text]}", text_options
              else
                text part[:text], text_options
              end
            end
          end
        end
      end
    end

    base64_images.each do |image_path|
      add_image_safely(docx, image_path)
    end

    base64_images
  end

  def process_html_content_with_answer_label(docx, html_content)
    return [] if html_content.blank?

    base64_images = extract_base64_images(html_content)
    clean_html = remove_base64_images_from_html(html_content)
    parsed_content = parse_html_with_formatting(clean_html)

    docx.p do
      text "Đáp án: ", bold: true, color: '008000', font: 'Times New Roman', size: 28

      if parsed_content.any? && parsed_content.first[:formatted_parts]
        parsed_content.first[:formatted_parts].each do |part|
          next if part[:text].blank?
          
          text_options = { font: 'Times New Roman', size: 28 }
          
          if part[:formatting][:bold]
            text_options[:bold] = true
          end
          
          if part[:formatting][:italic]
            text_options[:italic] = true
          end
          
          if part[:formatting][:underline]
            text_options[:underline] = true
          end

          if part[:formatting][:subscript]
            text "_#{part[:text]}", text_options
          elsif part[:formatting][:superscript]
            text "^#{part[:text]}", text_options
          else
            text part[:text], text_options
          end
        end
      else
        clean_text = clean_html_entities(clean_html)
        text clean_text, font: 'Times New Roman', size: 28 if clean_text.present?
      end
    end

    if parsed_content.length > 1
      parsed_content[1..-1].each do |element|
        if element[:type] == :paragraph && element[:formatted_parts]
          docx.p do
            element[:formatted_parts].each do |part|
              next if part[:text].blank?
              
              text_options = { font: 'Times New Roman', size: 28 }
              
              if part[:formatting][:bold]
                text_options[:bold] = true
              end
              
              if part[:formatting][:italic]
                text_options[:italic] = true
              end
              
              if part[:formatting][:underline]
                text_options[:underline] = true
              end

              if part[:formatting][:subscript]
                text "_#{part[:text]}", text_options
              elsif part[:formatting][:superscript]
                text "^#{part[:text]}", text_options
              else
                text part[:text], text_options
              end
            end
          end
        end
      end
    end

    base64_images.each do |image_path|
      add_image_safely(docx, image_path)
    end

    base64_images
  end

  def process_html_content_with_cleanup(docx, html_content, options = {})
    return [] if html_content.blank?

    base64_images = extract_base64_images(html_content)
    clean_html = remove_base64_images_from_html(html_content)
    parsed_content = parse_html_with_formatting(clean_html)

    add_formatted_content_to_docx(docx, parsed_content, options)

    base64_images.each do |image_path|
      add_image_safely(docx, image_path)
    end

    base64_images
  end

  def extract_base64_images(html_content)
    images = []
    base64_img_regex = /<img[^>]+src\s*=\s*["']data:image\/([^;]+);base64,([^"']+)["'][^>]*>/i
    
    html_content.scan(base64_img_regex) do |image_format, base64_data|
      begin
        image_data = Base64.decode64(base64_data)

        tmp_dir = Rails.root.join('tmp')
        FileUtils.mkdir_p(tmp_dir) unless Dir.exist?(tmp_dir)

        temp_filename = "temp_image_#{Time.current.to_i}_#{SecureRandom.hex(8)}.#{image_format}"
        temp_path = tmp_dir.join(temp_filename)

        File.binwrite(temp_path, image_data)

        if File.exist?(temp_path) && File.size(temp_path) > 0
          images << temp_path.to_s
          Rails.logger.info "Successfully created temp image: #{temp_path}"
        else
          Rails.logger.error "Failed to create temp image file: #{temp_path}"
        end
        
      rescue => e
        Rails.logger.error "Error processing base64 image: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end
    
    images
  end

  def remove_base64_images_from_html(html_content)
    base64_img_regex = /<img[^>]+src\s*=\s*["']data:image\/[^"']+["'][^>]*>/i
    html_content.gsub(base64_img_regex, '')
  end

  def calculate_image_dimensions(image_path, max_width = 250, max_height = 200)
    begin
      original_width = max_width
      original_height = max_height

      width_ratio = max_width.to_f / original_width
      height_ratio = max_height.to_f / original_height
      ratio = [width_ratio, height_ratio].min
      
      [(original_width * ratio).to_i, (original_height * ratio).to_i]
    rescue
      [max_width, max_height]
    end
  end

  def parse_html_with_formatting(html_content)
    return [] if html_content.blank?

    elements = []

    current_text = html_content.dup

    current_text.gsub!(/<p[^>]*>(.*?)<\/p>/mi) do |match|
      content = $1
      elements << { type: :paragraph, content: content }
      "\n"
    end

    current_text.gsub!(/<br\s*\/?>/i, "\n")

    remaining_text = current_text.gsub(/<[^>]+>/, '').strip
    unless remaining_text.empty?
      elements << { type: :paragraph, content: remaining_text }
    end

    elements.map do |element|
      if element[:type] == :paragraph
        element[:formatted_parts] = parse_formatting_tags(element[:content])
      end
      element
    end
  end

  def parse_formatting_tags(text)
    parts = []
    current_text = text.dup

    while current_text.present?
      if match = current_text.match(/<(b|strong|i|em|u|sub|sup)[^>]*>(.*?)<\/\1>/mi)

        before_tag = current_text[0...match.begin(0)]
        if before_tag.present?
          parts << { text: clean_html_entities(before_tag), formatting: {} }
        end

        tag_name = match[1].downcase
        tag_content = match[2]
        
        formatting = case tag_name
                    when 'b', 'strong' then { bold: true }
                    when 'i', 'em' then { italic: true }
                    when 'u' then { underline: true }
                    when 'sub' then { subscript: true }
                    when 'sup' then { superscript: true }
                    else {}
                    end
        
        parts << { text: clean_html_entities(tag_content), formatting: formatting }

        current_text = current_text[match.end(0)..-1]
      else
        if current_text.present?
          parts << { text: clean_html_entities(current_text), formatting: {} }
        end
        break
      end
    end
    
    parts
  end

  def clean_html_entities(text)
    return '' if text.blank?
    
    text.gsub(/&nbsp;/, ' ')
        .gsub(/&rarr;/, '→')
        .gsub(/&larr;/, '←')
        .gsub(/&minus;/, '−')
        .gsub(/&oacute;/, 'ó')
        .gsub(/&aacute;/, 'á')
        .gsub(/&eacute;/, 'é')
        .gsub(/&iacute;/, 'í')
        .gsub(/&uacute;/, 'ú')
        .gsub(/&yacute;/, 'ý')
        .gsub(/&agrave;/, 'à')
        .gsub(/&egrave;/, 'è')
        .gsub(/&igrave;/, 'ì')
        .gsub(/&ograve;/, 'ò')
        .gsub(/&ugrave;/, 'ù')
        .gsub(/&acirc;/, 'â')
        .gsub(/&ecirc;/, 'ê')
        .gsub(/&icirc;/, 'î')
        .gsub(/&ocirc;/, 'ô')
        .gsub(/&ucirc;/, 'û')
        .gsub(/&Delta;/, 'Δ')
        .gsub(/&amp;/, '&')
        .gsub(/&lt;/, '<')
        .gsub(/&gt;/, '>')
        .gsub(/&quot;/, '"')
        .gsub(/<[^>]+>/, '')
        .strip
  end

  def add_formatted_content_to_docx(docx, parsed_content, options = {})
    parsed_content.each do |element|
      if element[:type] == :paragraph && element[:formatted_parts]
        docx.p do
          element[:formatted_parts].each do |part|
            next if part[:text].blank?
            
            text_options = {}
            text_options[:font] = 28

            if part[:formatting][:bold]
              text_options[:bold] = true
            end
            
            if part[:formatting][:italic]
              text_options[:italic] = true
            end
            
            if part[:formatting][:underline]
              text_options[:underline] = true
            end

            if part[:formatting][:subscript]
              text "_#{part[:text]}", text_options
            elsif part[:formatting][:superscript]
              text "^#{part[:text]}", text_options
            else
              text part[:text], text_options
            end
          end
        end
      end
    end
  end

  def add_image_safely(docx, media_path)
    return unless media_path.present?

    unless File.exist?(media_path) && File.size(media_path) > 0
      return
    end
    
    begin
      width, height = calculate_image_dimensions(media_path)
      docx.img media_path, width: width, height: height
    rescue => e
    end
  end

  def cleanup_temp_file(file_path)
    return unless file_path.present?
    
    begin
      if File.exist?(file_path)
        File.delete(file_path)
      end
    rescue => e
    end
  end

  def format_chemical_formula(text)
    return text if text.blank?
    
    # Bảng chuyển đổi ký tự Unicode thành HTML tags
    unicode_map = {
      # Subscript numbers
      '₀' => '<sub>0</sub>', '₁' => '<sub>1</sub>', '₂' => '<sub>2</sub>',
      '₃' => '<sub>3</sub>', '₄' => '<sub>4</sub>', '₅' => '<sub>5</sub>',
      '₆' => '<sub>6</sub>', '₇' => '<sub>7</sub>', '₈' => '<sub>8</sub>',
      '₉' => '<sub>9</sub>',
      
      # Superscript numbers
      '⁰' => '<sup>0</sup>', '¹' => '<sup>1</sup>', '²' => '<sup>2</sup>',
      '³' => '<sup>3</sup>', '⁴' => '<sup>4</sup>', '⁵' => '<sup>5</sup>',
      '⁶' => '<sup>6</sup>', '⁷' => '<sup>7</sup>', '⁸' => '<sup>8</sup>',
      '⁹' => '<sup>9</sup>',
      
      # Superscript symbols
      '⁺' => '<sup>+</sup>', '⁻' => '<sup>-</sup>',
      
      # Common chemical combinations
      '²⁺' => '<sup>2+</sup>', '³⁺' => '<sup>3+</sup>', '⁴⁺' => '<sup>4+</sup>',
      '²⁻' => '<sup>2-</sup>', '³⁻' => '<sup>3-</sup>', '⁴⁻' => '<sup>4-</sup>',
    }
    
    formatted_text = text.dup
    unicode_map.each do |unicode, html|
      formatted_text.gsub!(unicode, html)
    end
    
    formatted_text.html_safe
  end
end
