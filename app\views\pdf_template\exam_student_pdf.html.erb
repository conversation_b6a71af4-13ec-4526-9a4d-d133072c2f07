<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <style>
        /* Giữ nguyên font Times cho text thông thường */
        @font-face {
            font-family: 'Times';
            font-weight: normal;
            src: url("<%= Rails.root.join('public', 'assets', 'fonts', 'times.svg') %>") format('svg');
        }

        @font-face {
            font-family: 'Times';
            font-weight: bold;
            src: url("<%= Rails.root.join('public', 'assets', 'fonts', 'times-bold.svg') %>") format('svg');
        }

        @font-face {
            font-family: 'Times';
            font-style: italic;
            src: url("<%= Rails.root.join('public', 'assets', 'fonts', 'times-italic.svg') %>") format('svg');
        }

        @font-face {
            font-family: 'Times-italic';
            font-weight: normal;
            src: url('<%= Rails.root.join('public', "assets", "fonts", 'times-italic.svg')%>') format('svg')
        }

        * {
            font-size: 12.5px!important;
        }

       .fw-bold {
            font-family: 'Times';
            font-weight: bold;
       }
        .fw-italic {
            font-family: 'Times';
            font-weight: italic;
        }

        /* Thêm fallback fonts cho Unicode */
        body, h3, span {
            font-family: 'Times', 'DejaVu Serif', 'Times New Roman', 'Noto Serif', serif !important;
        }

        /* Class đặc biệt cho chemical formulas */
        .chemical-formula {
            font-family: 'DejaVu Sans', 'Noto Sans', 'Arial Unicode MS', Arial, sans-serif !important;
        }

        /* Hỗ trợ subscript và superscript */
        sub, sup {
            font-size: 0.75em;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        sup {
            top: -0.5em;
        }

        sub {
            bottom: -0.25em;
        }

        /* Các class khác giữ nguyên */
        .a-container {
            display: flex;
            flex-direction: column;
            padding-left: 20px;
        }

        .choose-option::first-letter {
            margin-left: -10px
        }

        .choose-option > * {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
        }

        .q-title {
            display: flex;
            flex-wrap: nowrap;
            gap: 5px;
            width: 100%;
            align-items: center;
        }
        .q-title strong {
            white-space: nowrap;
            top: 0px;
            left: 0px;
        }

        .text-indent p {
            display: flex;
            align-items: center;
            margin: 0;
        }

        .selected {
            position: absolute;
            border: 1px solid;
            border-radius: 100%;
            width: 13.5px;
            height: 13.5px;
            right: -2px;
            top: -0.5px;
        }

        .p-margin-top-2 p {
            margin-top: 2px !important;
        }

        .p-margin-bottom-2 p {
            margin-bottom: 2px !important;
        }

        .p-margin-0 > p {
            margin: 0!important;
        }

        .always-break {
            page-break-before: always;
        }

        img {
            page-break-inside: avoid!important;
        }

        .text-top {
            vertical-align: top !important;
        }

        .text-nowrap {
            white-space: nowrap !important;
        }

        .text-end {
            text-align: end !important;
        }

        .text-center {
            text-align: center!important;
        }

        .m-0 {
            margin: 0!important;
        }

        .p-0 {
            padding: 0!important;
        }

        .relative {
            position: relative;
        }

        .absolute {
            position: absolute;
        }

        .italic {
            font-family: 'Times-italic'
        }
    </style>
</head>
<body>
    <!--  style="page-break-before:always; page-break-after:always" -->
        <!-- Thông tin chung -->
        <p class="m-0 text-center" style="display: inline-block;">
            <span>TRƯỜNG ĐẠI HỌC Y DƯỢC</span>
            <br/>
            <span class="fw-bold">BUÔN MA THUỘT</span>
            <br/>
            HỘI ĐỒNG THI KẾT THÚC HỌC PHẦN
        </p>


        <h3 class="fw-bold" style="font-size: 16px!important; width:100%; text-align: center;margin-bottom:5mm;margin-top:5mm">BÀI THI KẾT THÚC HỌC PHẦN</h3>

        <table>
            <tr>
                <td style="width: 20mm;vertical-align: top;">Học kỳ:</td>
                <td style="width: 50mm;vertical-align: top;"><%= semester  %></td>
                <td style="width: 20mm;vertical-align: top;">Năm học:</td>
                <td style="vertical-align: top;"><%= academic_year %></td>
            </tr>
            <tr>
                <td style="vertical-align: top;">Học phần:</td>
                <td style="vertical-align: top;"><%= subject_name %></td>
                <td style="vertical-align: top;">Mã học phần:</td>
                <td style="vertical-align: top;"><%= subject_code %></td>
            </tr>
            <tr>
                <td style="vertical-align: top;">Họ và tên:</td>
                <td style="vertical-align: top;"><%= student_name %></td>
                <td style="vertical-align: top;">Mã sinh viên:</td>
                <td style="vertical-align: top;"><%= student_code %></td>
            </tr>
            <tr>
                <td style="vertical-align: top;">Ngày thi:</td>
                <td style="vertical-align: top;"><%= exschedule_dtexam.in_time_zone("Asia/Ho_Chi_Minh").strftime('%d-%m-%Y') %></td>
                <td style="vertical-align: top;">Giờ thi:</td>
                <td style="vertical-align: top;"><%= exschedule_dtexam.in_time_zone("Asia/Ho_Chi_Minh").strftime('%H:%M') %></td>
            </tr>
            <tr>
                <td style="vertical-align: top;">Vào thi lúc:</td>
                <td style="vertical-align: top;"><%= start_time ? start_time.in_time_zone("Asia/Ho_Chi_Minh").strftime('%H:%M') : '' %></td>
                <td style="vertical-align: top;">Kết thúc lúc:</td>
                <td style="vertical-align: top;"><%= end_time ? end_time.in_time_zone("Asia/Ho_Chi_Minh").strftime('%H:%M') : '' %></td>
            </tr>
            <% if mexam_stype == 'Trắc nghiệm' %>
                <tr>
                    <td style="vertical-align: top;">Số câu đúng:</td>
                    <td style="vertical-align: top;"><%= total_correct %> / <%= total_ques %></td>
                    <td style="vertical-align: top;">Điểm:</td>
                    <td style="vertical-align: top;"><%= mark %></td>
                </tr>
            <% end %>
        </table>    

        <h4 class="fw-bold text-center" style="display: block; margin-top: 5mm; margin-bottom: 2mm;">Bài làm</h4>
        <div class="italic" style="width: fit-content; font-size: 11px; text-align: left;">
            (Ghi chú: * là đáp án đúng | khoanh tròn là đáp án sinh viên chọn)
        </div> 
        <!-- Câu trắc nghiêm -->
        <% if q_multi_choice_list.length > 0 %>
            <h4 class="fw-bold" style="margin-top: 3mm;">Phần I: Trắc nghiệm</h4>
            <% q_multi_choice_list&.each_with_index do |question, index|  %>
                <div class="q-container">
                    <!-- Câu hỏi -->
                    <table>
                        <td class="text-nowrap text-top">Câu <%= index + 1 %>:</td>
                        <td class="p-margin-0 text-top"><%= format_chemical_formula(question[:content]) %></td>

                         <!-- Hình ảnh minh hoạ -->
                         <tr>
                            <td></td>
                            <td style="max-width: 60mm;">
                                <% question[:media]&.each_with_index do |file, i|  %>
                                    <img src="<%= file %>" alt="" style="height: 20mm;">
                                <% end %>
                            </td>
                        </tr>
                        <!-- / -->

                        <!-- Đáp án -->
                        <% question[:options]&.each_with_index do |option, i|  %>
                            <tr>
                                <td class="text-end">
                                    <div class="text-top relative" style="padding-top: 1px; padding-bottom: 1px;">
                                        <!-- Đáp án đã chọn -->
                                        <% if question[:answer_id].to_i == option[:id].to_i %>
                                            <div class="selected"></div>
                                        <% end %>
                                        <!-- / -->
                                        <span>
                                            <%= question[:include_correct_answers] && option[:is_correct] ? '*' : "" %> 
                                            <%= (i + 1 + 96).chr.upcase %>:
                                        </span>
                                    </div>
                                </td>
                                <td style="padding: 0px;" class="p-margin-0"><%= option[:content].html_safe %></td>
                            </tr>



                        <% end %>
                        <!-- / -->
                    </table>
                    <!-- / -->
                </div>
            <% end %>
        <% end %>


        <div>
            <!-- Tự luận -->
            <% if q_qessay_list.length > 0 %>
            <h4 class="my-2 always-break">Phần II: Tự luận</h4>
            <!-- Danh sách các câu hỏi -->
            <% q_qessay_list&.each_with_index do |question, index|  %>
                <div class="q-container p-margin-top-2 p-margin-bottom-2">
                    <table>
                        <!-- Tiêu đề câu hỏi -->
                        <tr>
                            <td style="white-space: nowrap; vertical-align: top">
                                    Câu <%= index + 1 %>: 
                                </td>
                            <td style="padding-left: 5px;" class="p-margin-0">
                                <span class="chemical-formula"><%= question[:content].html_safe  %></span>
                            </td>
                        </tr>
                        <!-- / -->

                        <!-- Hình ảnh minh hoạ -->
                        <tr>
                            <td></td>
                            <td style="max-width: 60mm;">
                                <% question[:media]&.each_with_index do |file, i|  %>
                                    <img src="<%= file %>" alt="" style="height: 20mm;">
                                <% end %>
                            </td>
                        </tr>
                        <!-- / -->
                    </table>

                    <!-- Các ý trong câu hỏi -->
                    <% question[:options]&.each_with_index do |option, i|  %>
                        <div style="margin-bottom: 20px; padding-left: 30px;">
                            <!-- Nội dung ý -->
                            <div class=""><%= option[:title].html_safe %></div>
                            <!-- Ảnh minh hoạ -->
                            <div>
                                <% option[:media]&.each_with_index do |file, i|  %>
                                    <img src="<%= file %>" alt="" style="height: 20mm;">
                                <% end %>
                            </div>
                            <!-- Câu trả lời -->
                            <div>
                                <span style="white-space: nowrap;">Trả lời:</span> <%= option[:answer].html_safe %>
                            </div>
                        </div>
                    <% end %>
                    <!-- / -->
                </div>
            <% end %>
            <!-- / -->
        <% end %>
        <!-- / -->
        </div>
</body>
</html>