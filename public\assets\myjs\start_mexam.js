const data = {
    displayMode: 0,
    currentQuestion: 0,
    examData: null,
    exam_id: null,
    mexam_id: null,
    exscheduledetail_id: null,
    total_time: null,
    dtstart: null,
    dtend: null,
    zoomConfig: {
        min: 9, // 50% của 18px
        max: 36, // 200% của 18px
        step: 4.5, // 25% của 18px
        default: 18
    }
};
let examTimer;
let serverUpdateInterval;
let statusCheckInterval;
let isExamSubmitted = false;
let isNetworkError = false;
let remainingTime = 0;
let totalExamTime = 0;
let showSubmitButtonTime = 0;
let hasCompletedAllQuestions = false;
  
$(document).ready(function () {
    data.exam_id = $('#exam_id').val();
    data.mexam_id = $('#mexam_id').val();
    data.exscheduledetail_id = $('#exscheduledetail_id').val();
    localStorage.setItem('theme', 'light');
  
    // get data exam and quesitems
    getAllExamData();
    initializeFlagStates();
});

const getAllExamData = () => {
    hasCompletedAllQuestions = false;
    const params = {
        mexam_id: data.mexam_id,
        exscheduledetail_id: data.exscheduledetail_id
    }
    const apiGetQuestionData = $.ajax({
        url: `get_quesitems`,
        data: params,
        type: 'GET',
    })

    const apiGetExamData = $.ajax({
        url: `get_mexam_data`,
        data: params,
        type: 'GET',
    })

    const currentQuestionIndex = data.currentQuestion;
    const currentDisplayMode = data.displayMode;


    Promise.all([apiGetQuestionData, apiGetExamData])
    .then((responses) => {
        // Handler apiGetExamData
        $('#subject_name').text(responses[1].subject_name);
        $('#mexam_stype').text("Loại đề thi: " + responses[1].mexam_stype);

        remainingTime = responses[1].remaining_time;
        totalExamTime = responses[1].total_time;
        showSubmitButtonTime = responses[1].show_submit_button_time

        // Start timer
        if (['SURVEY', 'FINISH'].includes(responses[1].status)){
            clearInterval(examTimer);
            clearInterval(serverUpdateInterval);
            $('#countdown-timer').text('00:00');
            document.getElementById('quesitem_mexam').style.pointerEvents = 'none';
            document.getElementById('quesitem_mexam').style.userSelect = 'none';
        } else {
            startExamTimer();
        }

        // Handler apiGetQuestionData
        data.examData = responses[0];
        data.displayMode = currentDisplayMode;  // restore displayMode
        const flagStates = JSON.parse(localStorage.getItem('flagStates'));

        data.examData.questions.forEach((q, index) => {
            q.doubleCheck = flagStates[data.exam_id + '_' + (index + 1)] || false;
        });

        if (data.displayMode == 1) {
            renderQuestions();
            gotoQuestion(currentQuestionIndex);
        } else {
            renderQuestions(currentQuestionIndex);
        }

        updateNavigation();
        bindEvents();
        onScrollInQuestions();
    })
    .catch((error) => {
    })
    .finally(function() {
        updateQuestionProgress();
    });
}

function startExamTimer() {
    let lastUpdateTime = new Date();
    let elapsedSeconds = parseInt(localStorage.getItem('elapsedSeconds')) || 0;

    // Hàm kiểm tra status của bài thi
    function checkExamStatus() {
        $.ajax({
            url: 'get_exam_status',
            data: {
                exam_id: data.exam_id
            },
            method: 'POST',
            success: function(response) {
                if (response.status === 'STOP' || response.status === 'DEFER' || response.status === 'PREPARE') {
                    clearInterval(examTimer);
                    clearInterval(serverUpdateInterval);
                    clearInterval(statusCheckInterval);
                    localStorage.removeItem('elapsedSeconds');
                    clearFlagStates();
                    window.location.href = ROOT_PATH + "students/dashboard_student";
                }
            },
            error: function(xhr, status, error) {
                console.error("Error checking exam status:", error);
            }
        });
    }

    function updateTimerFromServer(isReload = false) {
        const currentTime = new Date();
        let elapsedSinceLastUpdate = Math.floor((currentTime - lastUpdateTime) / 1000);

        $.ajax({
            url: 'update_exam_time',
            data: {
                exam_id: data.exam_id,
                elapsed_time: elapsedSinceLastUpdate + elapsedSeconds,
                is_reload: isReload,
                mexam_id: data.mexam_id
            },
            method: 'POST',
            success: function(response) {
                remainingTime = response.remaining_time;
                status_exam = response.status;
                totalExamTime = response.total_time;
                lastUpdateTime = new Date();
                elapsedSeconds = 0;

                updateTimerDisplay(remainingTime);

                if (status_exam === 'STOP') {
                    clearInterval(examTimer);
                    clearInterval(serverUpdateInterval);
                    clearInterval(statusCheckInterval);
                    localStorage.removeItem('elapsedSeconds');
                    clearFlagStates();
                    window.location.href = ROOT_PATH + "students/dashboard_student";
                }

                if (remainingTime <= 0) {
                    stopTimerAndSubmitExam(true);
                } else if (remainingTime === 600) {
                    showTenMinutesWarning();
                }
            },
            error: function(xhr, status, error) {}
        });
    }

    function checkShowSubmitButton(elapsedTime) {
        let allAnswered = checkAllQuestionsAnswered();
        if (elapsedTime >= showSubmitButtonTime || allAnswered) {
            $('#submit-btn').prop('disabled', false);  // Enable button khi đã đủ thời gian hoặc tất cả câu hỏi đã được trả lời
        } else {
            $('#submit-btn').prop('disabled', true);   // Disable button khi chưa đủ thời gian và chưa trả lời hết câu hỏi
        }
    }    

    function countdown() {
        if (remainingTime > 0) {
            remainingTime--;
            elapsedSeconds++;
            updateTimerDisplay(remainingTime);
            checkShowSubmitButton(totalExamTime - remainingTime);
            localStorage.setItem('elapsedSeconds', elapsedSeconds);
        } else {
            stopTimerAndSubmitExam(true);
        }
    }

    serverUpdateInterval = setInterval(() => updateTimerFromServer(false), 60000); // Mỗi phút cập nhật thời gian lại 1 lần
    examTimer = setInterval(countdown, 1000); // Bắt đầu đếm ngược mỗi giây
    statusCheckInterval = setInterval(checkExamStatus, 10000); // Kiểm tra status mỗi 10 giây

    updateTimerFromServer(true); // Gọi cập nhật thời gian từ server lần đầu
    checkExamStatus(); // Kiểm tra status lần đầu
}

function stopTimerAndSubmitExam(type) {
    if (!isExamSubmitted) {
        isExamSubmitted = true;
        clearInterval(examTimer);
        clearInterval(serverUpdateInterval);
        localStorage.removeItem('elapsedSeconds');
        submitExam(type);
    }
}

function checkNetworkConnection() {
    if (navigator.onLine) {
        if (isNetworkError) {
            isNetworkError = false;
            alert("Kết nối mạng đã được khôi phục. Bài thi sẽ tiếp tục.");
            getAllExamData();
            document.getElementById('quesitem_mexam').style.pointerEvents = '';
            document.getElementById('quesitem_mexam').style.userSelect = '';
        }
    } else {
        if (!isNetworkError) {
            isNetworkError = true;
            alert("Đã mất kết nối mạng. Thời gian làm bài tạm dừng. Vui lòng kiểm tra kết nối của bạn.");
            clearInterval(examTimer);
            clearInterval(serverUpdateInterval);
            document.getElementById('quesitem_mexam').style.pointerEvents = 'none';
            document.getElementById('quesitem_mexam').style.userSelect = 'none';
        }
    }
}
  
// Thêm event listener để kiểm tra kết nối mạng
window.addEventListener('online', checkNetworkConnection);
window.addEventListener('offline', checkNetworkConnection);

function updateTimerDisplay(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    const display = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    $('#countdown-timer').text(display);
}

function initializeFlagStates() {
    if (!localStorage.getItem('flagStates')) {
        localStorage.setItem('flagStates', JSON.stringify({}));
    }
};

function clearFlagStates() {
    localStorage.removeItem('flagStates');
    initializeFlagStates();
};

function showTenMinutesWarning() {
    // Đếm số câu hỏi đã được trả lời đầy đủ (bao gồm cả trắc nghiệm và tự luận)
    const answeredQuestions = data.examData.questions.filter(q => {
        if (q.quesitem_stype === 'TRAC-NGHIEM') {
            return q.answer_id != null && q.answer_id !== undefined;
        } else if (q.quesitem_stype === 'TU-LUAN') {
            return q.options && q.options.every(option => option.answer && option.answer.trim() !== '');
        }
        return false;
    }).length;

    const totalQuestions = data.examData.questions.length;
    const unansweredQuestions = totalQuestions - answeredQuestions;
    const flaggedQuestions = countFlaggedQuestions();

    document.getElementById('answeredQuestions').textContent = answeredQuestions;
    document.getElementById('unansweredQuestions').textContent = unansweredQuestions;
    document.getElementById('flaggedQuestions').textContent = flaggedQuestions;

    const tenMinutesWarningModal = new bootstrap.Modal(document.getElementById('tenMinutesWarningModal'));
    tenMinutesWarningModal.show();
}

function submitExam(showTimeUpModal = false) {
    $.ajax({
        url: `submit_exam`,
        data: {
            exam_id: data.exam_id,
            mexam_id: data.mexam_id
        },
        type: 'POST',
        success: function(response) {
            if (showTimeUpModal) {
                $('#timeUpModal').modal('show');
            } else {
                location.reload();
            }
        },
        error: function(error) {}
    });
}

$('#submit-btn').on('click', function() {
    $('#completed-exam').modal('show');

    const answeredQuestionsComplete = data.examData.questions.filter(q => {
        if (q.quesitem_stype === 'TRAC-NGHIEM') {
            return q.answer_id != null && q.answer_id !== undefined;
        } else if (q.quesitem_stype === 'TU-LUAN') {
            return q.options && q.options.every(option => option.answer && option.answer.trim() !== '');
        }
        return false;
    }).length;

    const totalQuestions = data.examData.questions.length;
    const unansweredQuestionsComplete = totalQuestions - answeredQuestionsComplete;
    const flaggedQuestionsComplete = countFlaggedQuestions();

    document.getElementById('answeredQuestionsComplete').textContent = answeredQuestionsComplete;
    document.getElementById('unansweredQuestionsComplete').textContent = unansweredQuestionsComplete;
    document.getElementById('flaggedQuestionsComplete').textContent = flaggedQuestionsComplete;
});

$('#confirm-submit-btn').on('click', function() {
    $('#completed-exam').modal('hide');
    clearFlagStates();
    submitExam(false);
});

$('#agreeBtn').on('click', function() {
    $('#timeUpModal').modal('hide');
    location.reload();
});

// Gán sự kiện cho các button điều hướng và chế độ hiển thị
function bindEvents() {
    $('#prevBtn').on('click', () => {
        if (data.currentQuestion > 0) {
            data.currentQuestion--;
            onChangeQuestion();
        }
    });

    $('#nextBtn').on('click', () => {
        if (data.currentQuestion < data.examData.questions.length - 1) {
            data.currentQuestion++;
            onChangeQuestion();
        }
    });

    $('#prevVectorBtn').on('click', () => {
        scrollQuestions(-10);
    });

    $('#nextVectorBtn').on('click', () => {
        scrollQuestions(10);
    });

    $('#displayMode').on('change', () => {
        data.displayMode = $('#displayMode').val();
        changeDisplayMode(data.displayMode);
    });

    // Thêm sự kiện lắng nghe phím
    
};

function navigateQuestion(direction) {
    const newQuestion = data.currentQuestion + direction;
    if (newQuestion >= 0 && newQuestion < data.examData.questions.length) {
        data.currentQuestion = newQuestion;
        onChangeQuestion();
    }
}

// Đánh dấu câu trả lời
const onTickAnswer = (index, subquestId, answerId) => {
    const question = data.examData.questions[index - 1];

    if (question.quesitem_stype === 'TRAC-NGHIEM') {
        question.subquestid = subquestId;
        question.has_answer = true;
        question.answer_id = answerId;  // Cập nhật answer_id
        saveAnswer(index, subquestId, '', 'TRAC-NGHIEM'); // Gửi rỗng cho answerText vì đây là câu trắc nghiệm
    } else {
        const answerText = tinymce.get(`textarea_${question.quesitem_id}_${answerId}`).getContent();
        question.subquestid = subquestId;
        question.has_answer = true;
        saveAnswer(index, subquestId, answerText, 'TU-LUAN'); // Gửi answerText cho câu tự luận
    }

    updateNavigation();  // Cập nhật điều hướng ngay lập tức
    updateQuestionProgress();
};

// Lưu câu trả lời vào server
function saveAnswer(index, subquestId, answerText, stype) {
    const question = data.examData.questions[index - 1];
    const dataToSend = {
        exam_id: data.exam_id,
        quesitem_id: question.quesitem_id,
        subquestid: subquestId,
        mexam_id: data.mexam_id,
        answer: stype !== 'TRAC-NGHIEM' ? answerText : ''
    };

    $.ajax({
        url: 'save_answer',
        type: 'POST',
        data: dataToSend,
        success: function(response) {
            // Cập nhật trạng thái câu hỏi
            question.answer_id = subquestId;
            question.has_answer = true;
            
            // Cập nhật nội dung câu trả lời
            if (stype === 'TU-LUAN') {
                const option = question.options.find(opt => opt.id == subquestId);
                if (option) {
                    option.answer = answerText;
                }
            }
            
            updateNavigation();
            updateCurrentQuestionDisplay(index - 1);
            updateQuestionProgress();
        },
        error: function(error) {}
    });
};

function updateCurrentQuestionDisplay(questionIndex) {
    const question = data.examData.questions[questionIndex];
    if (!question) return;

    if (question.quesitem_stype === 'TRAC-NGHIEM') {
        $(`input[name="question_${question.quesitem_id}"][value="${question.answer_id}"]`).prop('checked', true);
    } else if (question.quesitem_stype === 'TU-LUAN') {
        question.options.forEach(option => {
            const editor = tinymce.get(`textarea_${question.quesitem_id}_${option.id}`);
            if (editor) {
                editor.setContent(option.answer || '');
            }
        });
    }
};

// Hiển thị các câu hỏi
const renderQuestions = (questionIndex = null) => {
    const questionContent = document.getElementById('mainQAContext');
    const flagStates = JSON.parse(localStorage.getItem('flagStates'));

    if (questionContent) {
        if (data.examData && Array.isArray(data.examData.questions) && data.examData.questions.length > 0) {
            const html =
                questionIndex === null
                    ? data.examData.questions.map((q, index) => questionHtml(q, index + 1, flagStates)).join('')
                    : questionHtml(data.examData.questions[questionIndex], questionIndex + 1, flagStates);
            
            // Ẩn skeleton sau khi render xong
            setTimeout(() => {
                questionContent.innerHTML = html;
                renderQuestionBtns();
                updateNavigation();
                initTextEditor();
                // Restore saved answers
                data.examData.questions.forEach((question, index) => {
                    if (question.has_answer) {
                        if (question.quesitem_stype === 'TRAC-NGHIEM') {
                            $(`input[name="question_${question.quesitem_id}"][value="${question.answer_id}"]`).prop('checked', true);
                        } else if (question.quesitem_stype === 'TU-LUAN') {
                            question.options.forEach(option => {
                                const editor = tinymce.get(`textarea_${question.quesitem_id}_${option.id}`);
                                if (editor) {
                                    editor.setContent(option.answer || '');
                                }
                            });
                        }
                    }
                });

                if (questionIndex === null) {
                    // Nếu hiển thị tất cả câu hỏi
                    data.examData.questions.forEach((question, index) => {
                        updateCurrentQuestionDisplay(index);
                    });
                } else {
                    // Nếu chỉ hiển thị một câu hỏi
                    updateCurrentQuestionDisplay(questionIndex);
                }
                ensureCurrentQuestionVisible();
            }, 0);
        } else {
            clearSavedTime();
            window.location.href = ROOT_PATH + "students/dashboard_student";
        }
    }
};

function removeFontSizeAttribute(html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    doc.querySelectorAll('p, span').forEach(element => {
        const style = element.getAttribute('style');
        if (style) {
            const newStyle = style.replace(/font-size:[^;]+;?/gi, '');
            if (newStyle.trim()) {
                element.setAttribute('style', newStyle);
            } else {
                element.removeAttribute('style');
            }
        }
    });

    return doc.body.innerHTML;
}

// Tạo giao diện HTML cho từng loại câu hỏi trắc nghiệm/tự luận
const questionHtml = (question, index, flagStates) => `
    <div id=q_${index} class="w-100 section p-5 bg-secondary-custom rounded-2" data-id="${index}">
        <div class="d-flex flex-column w-100">
            <div class="d-flex flex-column" id="question-content">
                <h2 class="text-black-900 fw-bold" style="font-size: 1.5em; color: #1D3048">Câu: ${index} (${question.quesitem_stype === 'TRAC-NGHIEM' ? 'Trắc nghiệm' : 'Tự luận'})</h2>
                <p class="fw-semi-bold text-black">
                    ${removeFontSizeAttribute(question?.quesitem_name ?? '')}
                </p>
            </div>
        </div>
        <hr>
        <div class="d-flex flex-column gap-2">
        ${question.options
            .map((answer, i) =>
            question.quesitem_stype === 'TRAC-NGHIEM'
                ? `
                <div class="form-check d-flex align-items-center gap-2">
                <input class="form-check-input flex-shrink-0" type="radio" ${question.answer_id == answer.id ? 'checked' : ''} name="question_${question.quesitem_id}" id="question_${question.quesitem_id}_${answer.id}" style="transform: translateY(-2px);" onclick="onTickAnswer(${index}, ${answer.id})">
                <label class="form-check-label m-0 flex-grow-1" for="question_${question.quesitem_id}_${answer?.id}" style="font-size: 0.95em">
                    <div class="m-0 bg-white rounded text-black answer-multi-choice d-flex align-items-center">
                        <strong class="fw-bold me-2">${numberToChar(i + 1)}. </strong>
                        <div id="question-content" class="d-flex align-items-center flex-grow-1">
                            ${removeFontSizeAttribute(answer?.title)}
                        </div>
                    </div>
                </label>
                </div>`
                : `
                <div class='ps-4'>
                <div class="bg-white rounded border border-1 text-black">
                    <p class="m-0 p-2 border-bottom">
                    <strong>${numberToChar(i + 1)}. </strong> ${answer.title}
                    </p>
                    ${answer.images && answer.images.length > 0 ? `
                    ${answer.images.map(image => `<img src="${IMAGE_PATH + image}" alt="" style="height: 15em; object-fit: contain; object-position: left; margin: 5px;">`).join('')}
                    ` : ''}
                    <textarea id="textarea_${question.quesitem_id}_${answer.id}" name="question_${question.quesitem_id}_${answer.id}" data-id='${answer.id}' data-q-index='${index}' class="editor">${answer.answer ?? ''}</textarea>
                </div>
                </div>`
            )
            .join('')}
        </div>

        <div class="mt-3">
            <input type="checkbox" class="btn-check" id="btn-check-outlined_${index}" autocomplete="off" 
                ${flagStates[data.exam_id + '_' + index] ? 'checked' : ''} 
                oninput="clickFlag(${index})">
            <label class="btn btn-outline-primary check-flag-btn" for="btn-check-outlined_${index}">
                <span class="far fa-flag"></span>
                Gắn cờ
            </label>
        </div>
    </div>`;

// Chuyển số sang ký tự ABCD để set vào các đáp án
const numberToChar = (number) => String.fromCharCode(number + 96).toUpperCase();

// Cập nhật điều hướng câu hỏi
const updateNavigation = () => {
    $('.question-nav .btn').each(function (index) {
        $(this).removeClass('active focused double-check');

        if (index >= data.examData.questions.length) return;

        const question = data.examData.questions[index];

        if (question.quesitem_stype === 'TRAC-NGHIEM' && question.answer_id) {
            $(this).addClass('focused');
        } else if (question.quesitem_stype === 'TU-LUAN') {
            const allAnswered = question.options.every(option => option.answer && option.answer.trim() !== '');
            if (allAnswered) {
                $(this).addClass('focused');
            }
        }

        if (index === data.currentQuestion) {
            $(this).addClass('active');
        }

        if (question?.doubleCheck) {
            $(this).addClass('double-check');
        }
    });

    $('#prevBtn').prop('disabled', data.currentQuestion === 0);
    $('#nextBtn').prop('disabled', data.currentQuestion === data.examData.questions.length - 1);
    // Check và set disabled button submit Nộp bài khi chưa hoàn thành tất cả câu hỏi
    if (!hasCompletedAllQuestions) {
        if (checkAllQuestionsAnswered()) {
            hasCompletedAllQuestions = true;
            $('#submit-btn').prop('disabled', false);
        } else {
            $('#submit-btn').prop('disabled', true);
        }
    }    
};

const checkAllQuestionsAnswered = () => {
    const allAnswered = data.examData.questions.every(q => {
        if (q.quesitem_stype === 'TRAC-NGHIEM') {
            return q.answer_id != null && q.answer_id !== undefined;
        } else if (q.quesitem_stype === 'TU-LUAN') {
            return q.options && q.options.every(option => option.answer && option.answer.trim() !== '');
        }
        return false;
    });

    return allAnswered;
};

// Khởi tạo trình soạn thảo văn bản
const initTextEditor = () => {
    tinymce.remove();
    tinymce.init({
        selector: 'textarea.editor',
        plugins: 'image',
        menubar: false,
        toolbar: 'undo redo | fontsizeselect | bold italic | link image',
        image_title: true,
        automatic_uploads: true,
        file_picker_types: 'image',
        height: 200,
        branding: false,
        file_picker_callback: (cb, value, meta) => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
        
            input.addEventListener('change', (e) => {
                const file = e.target.files[0];
                
                // Kiểm tra kích thước file
                const maxSize = 2 * 1024 * 1024; // 1MB
                if (file.size > maxSize) {
                    alert('Kích thước file quá lớn. Vui lòng chọn file nhỏ hơn 2MB.');
                    return;
                }

                const reader = new FileReader();
                reader.addEventListener('load', () => {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Resize image
                        const maxWidth = 800;
                        const maxHeight = 600;
                        let width = img.width;
                        let height = img.height;

                        if (width > height) {
                            if (width > maxWidth) {
                                height *= maxWidth / width;
                                width = maxWidth;
                            }
                        } else {
                            if (height > maxHeight) {
                                width *= maxHeight / height;
                                height = maxHeight;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const id = 'blobid' + (new Date()).getTime();
                            const blobCache =  tinymce.activeEditor.editorUpload.blobCache;
                            const blobInfo = blobCache.create(id, blob, blob.type);
                            blobCache.add(blobInfo);

                            cb(blobInfo.blobUri(), { title: file.name });
                        }, file.type);
                    };
                    img.src = reader.result;
                });
                reader.readAsDataURL(file);
            });
        
            input.click();
        },
        setup: function (editor) {
            editor.on('blur', function (e) {
                handleEditorBlur(editor);
            });

            // ngăn chặn việc dán nội dung vào text
            editor.on('paste', function(e) {
                e.preventDefault();
                tinyMCE.activeEditor.notificationManager.open({
                    text: 'Paste không được cho phép trong editor này.',
                    type: 'info',
                    timeout: 3000
                });
                return false;
            });
            editor.on('copy', function(e) {
                e.preventDefault();
                tinyMCE.activeEditor.notificationManager.open({
                    text: 'Copy không được cho phép trong editor này.',
                    type: 'info',
                    timeout: 3000
                });
                return false;
            });
            editor.on('cut', function(e) {
                e.preventDefault();
                tinyMCE.activeEditor.notificationManager.open({
                    text: 'Cut không được cho phép trong editor này.',
                    type: 'info',
                    timeout: 3000
                });
                return false;
            });
        },
    });
};

function updateQuestionProgress() {
    if (!data.examData || !data.examData.questions) {
        $('#question-progress').text('Đã làm: 0/0 câu');
        $('#flagged-count').text('Đánh dấu: 0');
        return;
    }

    const totalQuestions = data.examData.questions.length;

    // Đếm số câu hỏi đã trả lời đầy đủ
    const answeredQuestions = data.examData.questions.filter(q => {
        if (q.quesitem_stype === 'TRAC-NGHIEM') {
            return q.answer_id != null && q.answer_id !== undefined;
        } else if (q.quesitem_stype === 'TU-LUAN') {
            return q.options && q.options.every(option => option.answer && option.answer.trim() !== '');
        }
        return false;
    }).length;

    $('#question-progress').text(`Đã làm: ${answeredQuestions}/${totalQuestions} câu`);
    
    updateFlaggedCount();
};

const handleEditorBlur = (editor) => {
    const textarea = editor.getElement();
    const dataId = textarea.getAttribute('data-id');
    const qIndex = parseInt(textarea.getAttribute('data-q-index')) - 1;
    const answer = editor.getContent().trim();

    saveAnswer(qIndex + 1, dataId, answer, 'TU-LUAN'); // Lưu câu trả lời
    updateNavigation(); // Cập nhật điều hướng ngay lập tức
};

// Xử lý việc cuộn danh sách câu hỏi
function scrollQuestions(offset) {
    const questionNav = document.querySelector('.question-nav');
    const buttons = questionNav.querySelectorAll('.btn');
    const currentLeft = questionNav.scrollLeft;
    const widthPerButton = buttons[0].offsetWidth;
    questionNav.scrollLeft = currentLeft + widthPerButton * offset;

    document.getElementById('prevVectorBtn').disabled = questionNav.scrollLeft === 0;
    document.getElementById('nextVectorBtn').disabled =
        questionNav.scrollLeft + questionNav.clientWidth >= questionNav.scrollWidth;
};

// Xử lý việc hiển thị các nút câu hỏi
const renderQuestionBtns = () => {
    const fragment = document.createDocumentFragment();
    const flagStates = JSON.parse(localStorage.getItem('flagStates'));

    data.examData.questions.forEach((q, index) => {
        const btn = document.createElement('button');

        btn.className = 'btn btn-outline-secondary position-relative btn-has-flag';
        btn.textContent = index + 1;
        btn.dataset.question = index;
        btn.addEventListener('click', (e) => {
            data.currentQuestion = parseInt(e.target.dataset.question);
            onChangeQuestion();
            initTextEditor();
        });

        if (flagStates[data.exam_id + '_' + (index + 1)]) {
            const span = document.createElement('span');
            span.className = 'fas fa-flag position-absolute';
            span.style = 'top: -7px; left: -3px; color: #FFA500; transform: rotate(334deg); height: 16px';
            btn.appendChild(span);
        }

        fragment.appendChild(btn);
    });

    const questionNav = document.querySelector('.question-nav');
    questionNav.innerHTML = '';
    questionNav.appendChild(fragment);
};

// Xử lý việc thay đổi chế độ hiển thị
const changeDisplayMode = (selectedMode) => {
    const currentQuestionIndex = data.currentQuestion;
    data.displayMode = selectedMode;
    if (selectedMode == 1) {
        $('#prevVectorBtn').hide();
        $('#nextVectorBtn').hide();
        $('#mainQAContext').removeClass('bg-secondary-custom');
        renderQuestions();
        // Sau khi render xong, cuộn đến câu hiện tại
        setTimeout(() => {
            gotoQuestion(currentQuestionIndex);
        }, 300);
    } else {
        $('#prevVectorBtn').show();
        $('#nextVectorBtn').show();
        $('#mainQAContext').addClass('bg-secondary-custom');
        renderQuestions(currentQuestionIndex);
    }

    renderQuestionBtns();
    updateNavigation();
};

// Xử lý việc di chuyển đến 1 câu hỏi cụ thể
const gotoQuestion = (targetId) => {
    if (data.displayMode == 1) {
        const targetElement = $(`#q_${targetId + 1}`);
        if (targetElement.length) {
            const targetOffset = targetElement.offset().top - $('#mainQAContext').offset().top + $('#mainQAContext').scrollTop() - 30;
            $('#mainQAContext').animate({ scrollTop: targetOffset }, 300);
        }
    } else {
        renderQuestions(targetId);
    }
    
    data.currentQuestion = targetId;
    updateNavigation();
};

function ensureCurrentQuestionVisible() {
    if (data.displayMode == 1) {
        const currentQuestionElement = $(`#q_${data.currentQuestion + 1}`);
        if (currentQuestionElement.length) {
            const containerTop = $('#mainQAContext').offset().top;
            const containerBottom = containerTop + $('#mainQAContext').height();
            const elementTop = currentQuestionElement.offset().top;
            const elementBottom = elementTop + currentQuestionElement.outerHeight();

            if (elementTop < containerTop || elementBottom > containerBottom) {
                gotoQuestion(data.currentQuestion);
            }
        }
    }
};

// Xử lý việc cuộn trong danh sách câu hỏi
const onScrollInQuestions = () => {
    $('#mainQAContext').on('scroll', function () {
        let maxVisibleHeight = 0;
        let maxVisibleSectionId = '';

        $('#mainQAContext .section').each(function () {
        const section = $(this);
        const sectionTop = section.offset().top;
        const sectionBottom = sectionTop + section.outerHeight();
        const containerTop = $('#mainQAContext').offset().top;
        const containerBottom = containerTop + $('#mainQAContext').height();

        const visibleHeight = Math.min(sectionBottom, containerBottom) - Math.max(sectionTop, containerTop);

        if (visibleHeight > maxVisibleHeight) {
            maxVisibleHeight = visibleHeight;
            maxVisibleSectionId = $(this).data('id');
        }
        });

        if (maxVisibleSectionId || maxVisibleSectionId == 0) {
            data.currentQuestion = maxVisibleSectionId - 1;
            updateNavigation();
        }
    });
};

// Xử lý thay đổi câu hỏi
const onChangeQuestion = () => {
    if (data.displayMode == 0) {
        renderQuestions(data.currentQuestion);
    } else {
        gotoQuestion(data.currentQuestion);
        updateCurrentQuestionDisplay(data.currentQuestion);
    }
    updateNavigation();
    if (checkAllQuestionsAnswered()) {
        $('#submit-btn').prop('disabled', false);  // Enable button khi đã trả lời hết câu hỏi
    } else {
        $('#submit-btn').prop('disabled', true);   // Disable button khi chưa trả lời hết câu hỏi
    } 
    
    // Cập nhật trạng thái của switch
    const currentQuestion = data.examData.questions[data.currentQuestion];
    $(`#flexSwitchCheckDefault_${data.currentQuestion + 1}`).prop('checked', currentQuestion.doubleCheck);
};

function countFlaggedQuestions() {
    return data.examData.questions.filter(q => q.doubleCheck).length;
};

const clickFlag = (qIndex) => {
    const question = data.examData.questions[qIndex - 1];
    question.doubleCheck = !question.doubleCheck;
    
    // Lưu trạng thái vào localStorage
    const flagStates = JSON.parse(localStorage.getItem('flagStates'));
    flagStates[data.exam_id + '_' + qIndex] = question.doubleCheck;
    localStorage.setItem('flagStates', JSON.stringify(flagStates));
    
    updateFlagUI(qIndex, question.doubleCheck);
    updateNavigation();
    updateFlaggedCount();
    updateQuestionProgress();
};

function updateFlagUI(qIndex, isFlagged) {
    // Cập nhật checkbox
    $(`#btn-check-outlined_${qIndex}`).prop('checked', isFlagged);
    
    // Cập nhật nút câu hỏi
    const btn = $(`.question-nav button[data-question="${qIndex - 1}"]`);
    if (isFlagged) {
        if (!btn.find('.fas.fa-flag').length) {
            btn.append('<span class="fas fa-flag position-absolute" style="top: -7px; left: -3px; color: #FFA500; transform: rotate(334deg); height: 16px"></span>');
        }
    } else {
        btn.find('.fas.fa-flag').remove();
    }
    
    // Cập nhật trạng thái của switch nếu đang ở chế độ danh sách
    if (data.displayMode == 1) {
        $(`#flexSwitchCheckDefault_${qIndex}`).prop('checked', isFlagged);
    }
};

function updateFlaggedCount() {
    const flaggedCount = countFlaggedQuestions();
    $('#flagged-count').text(`Đã đánh dấu: ${flaggedCount}`);
};

const clickZoom = (flag) => {
    let currentFontSize = parseFloat($('#mainQAContext').css('font-size'))
    const display = $('#zoom-percent-display')
    if ((flag && currentFontSize >= data.zoomConfig.max) || (!flag && currentFontSize <= data.zoomConfig.min)) return;

    let newFontSize = flag ? currentFontSize + data.zoomConfig.step : currentFontSize - data.zoomConfig.step;

    const percent =  (newFontSize * 100) / data.zoomConfig.default

    display.text(`${Math.ceil(percent)}%`)

    $('#mainQAContext').css('font-size', newFontSize + 'px');
}

const clickZoomDefault = () => {
    $('#mainQAContext').css('font-size', data.zoomConfig.default + 'px');
    $('#zoom-percent-display').text('100%')
}

const loading = (flag) => {
    const loadingClass = 'loading-exam'

   if (flag) $('#top').addClass(loadingClass)
    else $('#top').removeClass(loadingClass)
}