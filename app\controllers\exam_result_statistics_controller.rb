# Author: <PERSON><PERSON><PERSON>
# Date: 13/09/2024
# Description: export student exam results statistics according to the original set of questions
require 'zip'

class ExamResultStatisticsController < ApplicationController
    include ExamResultStatisticsHelper
    before_action :authorize
    skip_before_action :is_access_url, only: [:index, :export_exam_result_excel, :export_exam_result_pdf]
    
    def index
        @stypes = Mexam.where(status: 'APPROVED').distinct.pluck(:stype)
        search = params[:search] || ''
        @stype = params[:stype]
      
        sql = Mexam.where(status: 'APPROVED')
                   .where("mexams.name LIKE ? OR mexams.stype LIKE ?", "%#{search}%", "%#{search}%")
                   .order(created_at: :desc)
        sql = sql.where("mexams.stype = ?", @stype) if @stype.present?
        paginated_sql = pagination_limit_offset(sql, 10)

        # get list of mexams with information about exams status
        # Khoa 14/09/2024
        @mexams = paginated_sql.map do |mexam|
            exschedules = Exschedule.where(mexam_id: mexam.id)
            exscheduledetails = Exscheduledetail.joins(:exschedule)
                                                .where(exschedules: { mexam_id: mexam.id })
                                                .where(exschedules: { status: 'APPROVE' })

            exams = Exam.joins(exscheduledetail: :exschedule)
                        .where.not(exams: { is_valid: 'NOT_ALLOW' })
                        .where(exschedules: { mexam_id: mexam.id })
            
            all_finished = exams.exists? && exams.all? { |exam| ["FINISH", "COMPLETE"].include?(exam.status) }
          
            {
                mexam: mexam,
                exams_present: exams.exists?,
                all_finished: all_finished,
                stype_exam: exschedules.first&.stype_exam == "THI-DI" ? "Thi lần 1" : exschedules.first&.stype_exam == "THI-LAI" ? "Thi Lại" : exschedules.first&.stype_exam == "THI-CAI-THIEN" ? "Thi cải thiện" : exschedules.first&.stype_exam.present? ? exschedules.first&.stype_exam : "Thi lần 1",
                exscheduledetails: exscheduledetails
            }
        end
    end      

    def export_exam_result_excel
        mexam = Mexam.find(params[:mexam_id])
        
        # Lấy tất cả các lịch thi đã được phê duyệt cho mexam này
        exschedules = Exschedule.where(mexam_id: mexam.id).where(status: 'APPROVE')
        
        temp_dir = Rails.root.join('tmp', 'exam_exports', SecureRandom.hex)
        FileUtils.mkdir_p(temp_dir)
        
        # Tạo zip file
        zip_filename = "MaDe_#{mexam.id}_ngay_xuat_#{Time.current.strftime('%d_%m_%Y')}.zip"
        zip_path = Rails.root.join('tmp', zip_filename)
        
        # Group lịch thi theo ngày và loại thi
        grouped_schedules = exschedules.group_by do |schedule|
            [schedule.stype_exam, schedule.dtexam.to_date.strftime('%Y-%m-%d')]
        end
        
        Zip::File.open(zip_path, Zip::File::CREATE) do |zipfile|
          # Duyệt qua từng nhóm lịch thi
          grouped_schedules.each do |group_key, schedules|
            stype_exam, exam_date = group_key
            schedule_ids = schedules.map(&:id)
            first_schedule = schedules.first
            
            # Lọc các bài thi theo nhóm lịch thi
            exams = Exam.joins(exscheduledetail: :exschedule)
                        .where(
                          exschedules: { 
                            id: schedule_ids, 
                            mexam_id: mexam.id 
                          }
                        )
                        .select(
                          'exams.id,
                          exams.student_id,
                          exams.status,
                          exams.mark, 
                          exscheduledetails.id as exscheduledetail_id,
                          exschedules.dtexam as exschedule_dtexam'
                        )
            
            # Lọc câu hỏi theo loại thi và nhóm của lịch thi đầu tiên trong nhóm
            quesitems = Quesitem.joins(:mexam)
                                .where(
                                  mexams: { id: mexam.id },
                                  quesitems: { 
                                    smethod: first_schedule.stype_exam, 
                                    ctg: first_schedule.ctg 
                                  }
                                )
            
            # Tạo package Excel
            p = Axlsx::Package.new
            wb = p.workbook
            
            # Tạo worksheet - sử dụng ngày tháng không có giờ
            formatted_date = Date.parse(exam_date).strftime('%d_%m_%Y')
            wb.add_worksheet(name: "#{stype_exam}_#{formatted_date}") do |sheet|
              # Tạo header
              header = ['No', 'StudentID', 'MexamID']
              quesitems.each_with_index do |quesitem, index|
                header << (index + 1).to_s
              end
              sheet.add_row header
              
              # Tạo dòng đáp án
              answer_row = ["", "Đáp án", mexam.id]
              quesitems.each do |quesitem|
                qmultichoices = Qmultichoice.where(quesitem_id: quesitem.id)
                correct_choice = qmultichoices.find { |choice| choice.is_correct == "1" }
                correct_answer = case correct_choice&.id
                                  when qmultichoices[0]&.id then "A"
                                  when qmultichoices[1]&.id then "B"
                                  when qmultichoices[2]&.id then "C"
                                  when qmultichoices[3]&.id then "D"
                                  else ""
                                  end
                answer_row << correct_answer
              end
              sheet.add_row answer_row
              
              # Thêm dữ liệu từng bài thi
              exams.each_with_index do |exam, index|
                row = [
                  index + 1,
                  exam.student_id,
                  mexam.id
                ]
                
                # Thêm câu trả lời của sinh viên
                quesitems.each do |quesitem|
                  excontent = Excontent.find_by(
                    exam_id: exam.id, 
                    quesitem_id: quesitem.id
                  )
                  
                  if excontent
                    qmultichoices = Qmultichoice.where(quesitem_id: quesitem.id)
                    selected_choice = Qmultichoice.find_by(
                      id: excontent.subquestid, 
                      quesitem_id: quesitem.id
                    )
      
                    selected_answer = case selected_choice&.id
                                      when qmultichoices[0]&.id then "A"
                                      when qmultichoices[1]&.id then "B"
                                      when qmultichoices[2]&.id then "C"
                                      when qmultichoices[3]&.id then "D"
                                      else ""
                                      end
                    row << selected_answer
                  else
                    row << "*"
                  end
                end
                
                sheet.add_row row
              end
            end
            
            # Tạo tên file Excel với stype_exam và ngày không có giờ
            excel_filename = "MaDe#{mexam.id}_#{schedule_ids.join('_')}_thi_ngay_#{formatted_date}_#{stype_exam}.xlsx"
            excel_path = File.join(temp_dir, excel_filename)
            p.serialize(excel_path)
            
            # Thêm file Excel vào zip
            zipfile.add(excel_filename, excel_path)
          end
        end
        
        send_data File.read(zip_path),
                  filename: zip_filename,
                  type: 'application/zip',
                  disposition: 'attachment'
      

        FileUtils.rm_rf(temp_dir)
        FileUtils.rm_f(zip_path)
    end

    # Export kết quả thi làm minh chứng
    def export_exam_result_pdf
        exscheduledetail_id = params[:exscheduledetail_id]
        data_allow = []
        data_not_allow = []
        data_violation = []
        class_name = []
        oExscheduledetail = Exscheduledetail.select("exscheduledetails.id,
                                                     exscheduledetails.invigilators,
                                                     mmodules.credit_total,
                                                     subjects.name as sub_name,
                                                     subjects.scode as sub_scode,
                                                     DATE_FORMAT(TIMESTAMP(exschedules.dtexam), '%d/%m/%Y') as dtexam,
                                                     exschedules.syear as year,
                                                     mexams.extime as total_time,
                                                     exscheduledetails.room as room,
                                                     exschedules.stype_exam,
                                                     exscheduledetails.dtstart as exd_dtstart,
                                                     mexams.stype, mexams.id as mexam_id")
                                            .joins(exschedule: {mexam: {mmodule: [:subject, :mprogram]}}).where(exscheduledetails: {id: exscheduledetail_id}).first
        
        stype_exam = oExscheduledetail&.stype_exam
        ctg_schedule = oExscheduledetail&.ctg
        mexam = Mexam.find(oExscheduledetail.mexam_id)
        total_quesitems =
            if ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && ctg_schedule.nil?
              mexam.quesitems.where(smethod: stype_exam).includes(:qmultichoices, :qessays, :docs).count
            elsif ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && !ctg_schedule.nil?
              mexam.quesitems.where(smethod: stype_exam)
                                                .where(ctg: ctg_schedule)
                                                .includes(:qmultichoices, :qessays, :docs).count
            else
              mexam.quesitems.includes(:qmultichoices, :qessays, :docs).count
            end

        if !oExscheduledetail.nil?
            oExams = Exam.select("exams.id, exams.mark, exams.sviolation_type, exams.note, aclasses.name as class_name, exams.status, concat(students.last_name,' ',students.first_name) as stu_name, students.sid as stu_sid, DATE_FORMAT(CONVERT_TZ(students.birthday, '+00:00', '+07:00'), '%d/%m/%Y') as birthday, students.id as student_id").joins(student: {initclasses: :aclass}, exscheduledetail: :exschedule).where(exscheduledetails: {id: oExscheduledetail.id}).order("students.sid ASC, students.first_name ASC")
            student_allow = oExams.where(exams: {is_valid: "ALLOW"}).where.not(exams: {sviolation_type: ["BIEN-BAN", "CANH-BAO", "KHIEN-TRACH", "DINH-CHI"]})
            student_allow.each_with_index do |exam,index| 
                # is_finish = exam.status == "FINISH"
                exam_result = Exam.get_info_exam(exam.student_id, oExscheduledetail.id)
                exact_mark = exam_result.count_done.to_f / exam_result.total.to_i * 10.0

                data_allow.push([
                    index+1, 
                    exam.stu_sid,
                    exam.stu_name,
                    exam_result.count_done,
                    exam_result.count_fail,
                    exam.mark,
                    exact_mark.round(2)
                ]);
            end  
            class_name.concat(student_allow.map(&:class_name))

            student_violation = oExams.where(exams: {is_valid: "ALLOW", sviolation_type: ["BIEN-BAN", "CANH-BAO", "KHIEN-TRACH", "DINH-CHI"]})
            student_violation.each_with_index do |exam,index| 
                # is_finish = exam.status == "FINISH"
                exam_result = Exam.get_info_exam(exam.student_id, oExscheduledetail.id)
                exact_mark = (exam_result.count_done.to_f / exam_result.total.to_i) * 10.0

                data_allow.push([
                    index+1, 
                    exam.stu_sid,
                    exam.stu_name,
                    exam_result.count_done,
                    exam_result.count_fail,
                    exam.mark,
                    exact_mark.round(2)
                ]);
            end  
            class_name.concat(student_violation.map(&:class_name))

            student_not_allow = oExams.where("exams.is_valid IS NULL OR exams.is_valid != ?", "ALLOW")
            student_not_allow.each_with_index do |exam,index| 
                data_not_allow.push([
                    index+1, 
                    exam.stu_sid, 
                    exam.stu_name, 
                    "", 
                    exam.birthday, 
                ]);
            end
            class_name.concat(student_not_allow.map(&:class_name))
            class_name = class_name.uniq

            subject_scode = oExscheduledetail.sub_scode
            file_name = "KẾT QUẢ THI KẾT THÚC HỌC PHẦN #{subject_scode} NĂM HỌC #{oExscheduledetail.year.split("$$$")[1]} - #{oExscheduledetail.year.split("$$$")[1].to_i + 1}"

            time_now_format = Time.now.strftime("%d/%m/%Y")
            html_content = render_to_string(
                template: 'pdf_template/pdf_export_exam_result_template',
                layout: false,
                locals: { 
                    data_allow: data_allow, 
                    data_not_allow: data_not_allow, 
                    data_violation: data_violation, 
                    file_name: file_name,
                    subject_scode: subject_scode, 
                    exscheduledetail: oExscheduledetail, 
                    class_name: class_name,
                    total_quesitems: total_quesitems
                }
            )

            html_footer = render_to_string(
                template: 'dashboard/pdf_footer_exam_template', 
                layout: false,                
                locals: { class_name: class_name, subject_scode: subject_scode }  
            )
            # Sử dụng wicked_pdf để tạo file PDF từ HTML
            pdf = WickedPdf.new.pdf_from_string(
                html_content,
                footer: {
                  spacing: 2,
                  content: html_footer, 
                  font_size: 12, 
                  line: false,
                },
                header: {
                  spacing: 2,
                  right: '[page] / [topage]',
                  font_size: 8, 
                  line: false,
                },
                margin: {
                    top: 15,    
                    bottom: 20, 
                    left: 10,   
                    right: 10   
                }
            )
            
            file_path = "/data/sftraining/tmp/#{file_name}.pdf"

            File.open(file_path, 'wb') do |file|
                file << pdf
            end
        
            send_file(file_path, type: 'application/pdf', disposition: 'inline') do
                File.delete(file_path) if File.exist?(file_path)
            end

        else
            redirect_to :back, notice: "Không tìm thấy danh sách sinh viên"
        end
    end

    def export_exam_result_excel_proof
      mexam_id = params[:mexam_id]
      
      # Lấy danh sách các exscheduledetail từ mexam_id
      exscheduledetails = Exscheduledetail.joins(:exschedule)
                                          .where(exschedules: { mexam_id: mexam_id })
                                          .where(exschedules: { status: 'APPROVE' })
                                          .where.not(dtstart: nil )
      
      if exscheduledetails.blank?
        redirect_to :back, notice: "Không tìm thấy danh sách kỳ thi" and return
      end
      
      # Lấy thông tin mexam để đặt tên file
      mexam = Mexam.find(mexam_id)
      mmodule = mexam.mmodule
      subject = mmodule.subject
      file_name = "KET_QUA_THI_#{subject.scode}_#{Time.now.strftime('%d%m%Y')}"
      
      # Tạo Excel file với axlsx
      p = Axlsx::Package.new
      wb = p.workbook
      
      # Define styles
      styles = wb.styles
      header_style = styles.add_style(b: true, sz: 10, alignment: { horizontal: :center, vertical: :center }, border: { style: :thin, color: 'FF000000' })
      cell_style = styles.add_style(sz: 10, alignment: { vertical: :center })
      title_style = styles.add_style(b: true, sz: 14, alignment: { horizontal: :center })
      subtitle_style = styles.add_style(b: true, sz: 10)
      center_style = styles.add_style(sz: 10, alignment: { horizontal: :center, vertical: :center }, border: { style: :thin, color: 'FF000000' })
      
      # Xử lý từng exscheduledetail và tạo sheet
      exscheduledetails.each do |exscheduledetail|
        data_allow = []
        data_not_allow = []
        data_violation = []
        class_name = []
        
        oExscheduledetail = Exscheduledetail.select("exscheduledetails.id,
                                                  exscheduledetails.invigilators,
                                                  mmodules.credit_total,
                                                  subjects.name as sub_name,
                                                  subjects.scode as sub_scode,
                                                  DATE_FORMAT(TIMESTAMP(exschedules.dtexam), '%d/%m/%Y') as dtexam,
                                                  exschedules.syear as year,
                                                  mexams.extime as total_time,
                                                  exscheduledetails.room as room,
                                                  exschedules.stype_exam,
                                                  exschedules.ctg as ctg,
                                                  exscheduledetails.dtstart as exd_dtstart,
                                                  mexams.stype, mexams.id as mexam_id")
                                          .joins(exschedule: {mexam: {mmodule: [:subject, :mprogram]}})
                                          .where(exscheduledetails: {id: exscheduledetail.id})
                                          .first

        stype_exam = oExscheduledetail&.stype_exam
        ctg_schedule = oExscheduledetail&.ctg
        mexam = Mexam.find(oExscheduledetail.mexam_id)
        total_quesitems =
            if ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && ctg_schedule.nil?
              mexam.quesitems.where(smethod: stype_exam).includes(:qmultichoices, :qessays, :docs).count
            elsif ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && !ctg_schedule.nil?
              mexam.quesitems.where(smethod: stype_exam)
                                              .where(ctg: ctg_schedule)
                                              .includes(:qmultichoices, :qessays, :docs).count
            else
              mexam.quesitems.includes(:qmultichoices, :qessays, :docs).count
            end
        
        if !oExscheduledetail.nil?
          oExams = Exam.select("exams.id, exams.mark, exams.sviolation_type, exams.note, aclasses.name as class_name, exams.status, concat(students.last_name,' ',students.first_name) as stu_name, students.sid as stu_sid, DATE_FORMAT(CONVERT_TZ(students.birthday, '+00:00', '+07:00'), '%d/%m/%Y') as birthday, students.id as student_id")
                      .joins(student: {initclasses: :aclass}, exscheduledetail: :exschedule)
                      .where(exscheduledetails: {id: oExscheduledetail.id})
                      .order("students.sid ASC, students.first_name ASC")
          
          student_allow = oExams.where(exams: {is_valid: "ALLOW"})
                                .where.not(exams: {sviolation_type: ["BIEN-BAN", "CANH-BAO", "KHIEN-TRACH", "DINH-CHI"]})
          
          student_allow.each_with_index do |exam, index| 
            exam_result = Exam.get_info_exam(exam.student_id, oExscheduledetail.id)
            exact_mark = exam_result.count_done.to_f / exam_result.total.to_i * 10.0
            
            # Thêm cột ngày sinh sau cột họ tên
            data_allow.push([
              index+1, 
              exam.stu_sid,
              exam.stu_name,
              exam.birthday.present? ? exam.birthday : "", # Cột ngày sinh
              exam_result.count_done,
              exam_result.count_fail,
              exact_mark.round(2)
            ])
          end
          class_name.concat(student_allow.map(&:class_name))
          
          student_violation = oExams.where(exams: {is_valid: "ALLOW", sviolation_type: ["BIEN-BAN", "CANH-BAO", "KHIEN-TRACH", "DINH-CHI"]})
          student_violation.each_with_index do |exam, index| 
            exam_result = Exam.get_info_exam(exam.student_id, oExscheduledetail.id)
            exact_mark = (exam_result.count_done.to_f / exam_result.total.to_i) * 10.0
            
            # Thêm cột ngày sinh sau cột họ tên
            data_violation.push([
              index+1, 
              exam.stu_sid,
              exam.stu_name,
              exam.birthday.present? ? exam.birthday : "", # Cột ngày sinh
              exam_result.count_done,
              exam_result.count_fail,
              exact_mark.round(2)
            ])
          end
          class_name.concat(student_violation.map(&:class_name))
          
          student_not_allow = oExams.where("exams.is_valid IS NULL OR exams.is_valid != ?", "ALLOW")
          student_not_allow.each_with_index do |exam, index| 
            # Thêm cột ngày sinh cho danh sách sinh viên vắng
            data_not_allow.push([
              index+1, 
              exam.stu_sid, 
              exam.stu_name,
              exam.birthday.present? ? exam.birthday : "", # Cột ngày sinh
            ])
          end
          class_name.concat(student_not_allow.map(&:class_name))
          class_name = class_name.uniq
          
          # Calculate statistics
          total_pass = data_allow.count { |row| row[6].to_i >= 4 }
          total_fail = data_allow.count { |row| row[6].to_i < 4 }
          
          # Tạo sheet name là id_dtstart
          room_name = oExscheduledetail.room ? oExscheduledetail.room.split('$$$')[1] : ""
          sheet_name = "#{oExscheduledetail.id}-#{oExscheduledetail.exd_dtstart.strftime('%d%m%Y')}-#{room_name}"
          
          # Nếu tên sheet quá dài, cắt bớt để phù hợp với giới hạn của Excel (31 ký tự)
          if sheet_name.length > 31
            sheet_name = sheet_name[0..30]
          end
          
          # Create worksheet
          wb.add_worksheet(name: sheet_name) do |sheet|
            # Title (merge thêm 1 cột)
            sheet.add_row ["KẾT QUẢ THI KẾT THÚC HỌC PHẦN #{oExscheduledetail.sub_scode} NĂM HỌC #{oExscheduledetail.year.split("$$$")[1]} - #{oExscheduledetail.year.split("$$$")[1].to_i + 1}"], style: title_style
            sheet.merge_cells "A1:G1"
            sheet.add_row []
            
            # Details (thêm 1 cột trống)
            sheet.add_row ["Bậc đào tạo:", "Đại học", "Học kỳ:", oExscheduledetail.year.split('$$$')[0], "Số tín chỉ:", oExscheduledetail.credit_total, ""], style: cell_style
            sheet.add_row ["Tên lớp:", class_name&.flatten.join(', '), "Mã lớp:", class_name&.flatten.join(', '), "Loại thi:", oExscheduledetail.stype_exam, ""], style: cell_style
            sheet.add_row ["Tên học phần:", oExscheduledetail.sub_name, "Mã học phần:", oExscheduledetail.sub_scode, "", "", ""], style: cell_style
            sheet.add_row ["Thời gian bắt đầu thi:", (oExscheduledetail.exd_dtstart + 7.hours).strftime("%H:%M %d/%m/%Y"), "Thời gian làm bài:", "#{oExscheduledetail.total_time} phút", "", "", ""], style: cell_style
            sheet.add_row ["Phòng thi:", oExscheduledetail.room ? oExscheduledetail.room.split('$$$')[1] : "", "Hình thức thi:", "#{oExscheduledetail.stype} trên máy", "Tổng số câu thi:", total_quesitems, ""], style: cell_style
            sheet.add_row []
            
            # Students allowed to take exam
            sheet.add_row ["DANH SÁCH SINH VIÊN DỰ THI"], style: subtitle_style
            sheet.merge_cells "A8:G8"
            
            # Headers for allowed students (thêm cột Ngày sinh)
            sheet.add_row ["TT", "MSSV", "Họ và tên", "Ngày sinh", "Số câu đúng", "Số câu sai", "Điểm"], style: header_style
            
            # Data for allowed students
            data_allow.each do |row|
              sheet.add_row row, style: [center_style, center_style, center_style, center_style, center_style, center_style, center_style]
            end
            
            # Statistics for allowed students
            sheet.add_row []
            sheet.add_row ["Số thí sinh dự thi: #{data_allow.size} sinh viên", "Số bài thi đạt: #{total_pass}", "Số bài thi không đạt: #{total_fail}"], style: cell_style
            sheet.add_row []
            
            # Students with violations
            next_row = sheet.rows.count + 1
            sheet.add_row ["SINH VIÊN BỊ LẬP BIÊN BẢN"], style: subtitle_style
            sheet.merge_cells "A#{next_row}:G#{next_row}"
            
            # Headers for students with violations (thêm cột Ngày sinh)
            sheet.add_row ["TT", "MSSV", "Họ và tên", "Ngày sinh", "Số câu đúng", "Số câu sai", "Điểm"], style: header_style
            
            # Data for students with violations
            data_violation.each do |row|
              sheet.add_row row, style: [center_style, center_style, center_style, center_style, center_style, center_style, center_style]
            end
            
            # Statistics for students with violations
            sheet.add_row []
            sheet.add_row ["Tổng số thí sinh bị lập biên bản: #{data_violation.size} sinh viên"], style: cell_style
            sheet.add_row []
            
            # Absent students
            next_row = sheet.rows.count + 1
            sheet.add_row ["SINH VIÊN VẮNG"], style: subtitle_style
            sheet.merge_cells "A#{next_row}:G#{next_row}"
            
            # Headers for absent students (thêm cột Ngày sinh)
            sheet.add_row ["TT", "MSSV", "Họ và tên", "Ngày sinh"], style: header_style
            
            # Data for absent students
            data_not_allow.each do |row|
              sheet.add_row [row[0], row[1], row[2], row[3]], style: [center_style, center_style, cell_style, center_style]
            end
            
            # Statistics for absent students
            sheet.add_row []
            sheet.add_row ["Tổng số thí sinh vắng: #{data_not_allow.size} sinh viên"], style: cell_style
            sheet.add_row []
            
            # Signature section
            next_row = sheet.rows.count + 1
            sheet.add_row ["PHÒNG KT&BĐCL", "NGƯỜI LẬP"], style: subtitle_style
            sheet.add_row ["(Ký, ghi rõ họ tên)", "(Ký, ghi rõ họ tên)"], style: cell_style
            sheet.add_row []
            sheet.add_row []
            sheet.add_row []
            sheet.add_row ["", "Ý KIẾN CỦA PHÓ HIỆU TRƯỞNG PHỤ TRÁCH", ""], style: subtitle_style
            sheet.add_row ["", "KHẢO THÍ VÀ ĐẢM BẢO CHẤT LƯỢNG", ""], style: subtitle_style
            
            # Set column widths (thêm width cho cột ngày sinh)
            sheet.column_widths 10, 15, 40, 15, 15, 15, 20
          end
        end
      end
      
      # Generate the file and send it to the user
      file_path = "/data/sftraining/tmp/#{file_name}.xlsx"
      p.serialize(file_path)
      
      send_file(file_path, type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", filename: "#{file_name}.xlsx", disposition: 'attachment') do
        File.delete(file_path) if File.exist?(file_path)
      end
    end

    # Nguyen Xuan Khoa
    # 20/09/2024
    # Lấy danh sách lịch thi
    def exam_results
        search = "%#{params[:search]}%"

        @year = params[:year]

        if !@year
            @year = Semester.where('dt_from >= :year AND dt_to <= :year', year: Date.today).first&.year
        end
        # Data for option to choose modules
        @mmodules = Mexam.joins(exschedules: :exscheduledetails).group(:mmodule_id).map do |m_exam|
            {
                id: m_exam&.mmodule_id,
                subject_name: m_exam&.mmodule&.subject&.name
            }
        end
        exscheduledetail = Exscheduledetail.joins(exschedule: {mexam: {mmodule: :subject}})
                                            .select('
                                                exscheduledetails.*,
                                                exschedules.dtexam,
                                                subjects.name
                                            ')
                                            .where("subjects.name LIKE :search OR exscheduledetails.room LIKE :search", search: search)
                                            .where(exschedules: { status: 'APPROVE' })
                                            .order('exschedules.dtexam DESC')

        # Mapping data to the response
        @data = exscheduledetail&.map do |exam_schedule_detail|
            {
                id: exam_schedule_detail.id,
                exam_date: exam_schedule_detail&.dtexam&.in_time_zone("Asia/Ho_Chi_Minh")&.strftime('%d-%m-%Y'),
                subject_name: exam_schedule_detail&.exschedule&.mexam&.mmodule&.subject&.name,
                room: exam_schedule_detail&.room&.split('$$$')&.dig(1),
                total_student: exam_schedule_detail&.exams.count,
                mexam_id: exam_schedule_detail&.exschedule&.mexam_id
            }
        end

        respond_to do |format|
            format.html { render '/exam_manager/exam_results' }
        end
    end

    # Nguyen Xuan Khoa
    # 20/09/2024
    # Lấy danh sách các bài thi theo lịch thi
    def get_student_list
        exam_schedule_detail_id = params[:exam_schedule_detail_id].to_i

        exams = Exam.joins(:exscheduledetail)
                    .joins(:student)
                    .where(exscheduledetails: {id: exam_schedule_detail_id})
                    .where(is_valid: 'ALLOW')
                    .distinct

         # Mapping data for table students
         mapped_exams = exams.map do |exam|
            {
                exam_id: exam&.id,
                student_code: exam&.student&.sid,
                student_name: "#{ exam&.student&.last_name } #{ exam&.student&.first_name }",
            }
        end

        respond_to do |format|
            format.html {
                render partial: '/exam_manager/student_exams_result_tbl.html.erb',
                locals: {
                    data: {
                        exams: exams,
                        mapped_exams: mapped_exams,
                        exam_schedule_detail_id: exam_schedule_detail_id,
                        search: params[:search],
                    }
                }
            }
        end
    end

    # Nguyen Xuan Khoa
    # 20/09/2024
    # Xuất file PDF khi yểu cầu xuất 1 bài, xuất file zip khi yêu cầu xuất nhiều bài
    def export_exams
        ids = params[:exam_ids]&.split(',')&.map(&:to_i)
        include_correct_answers = params[:include_correct_answers] == "1"

        begin
            exams = Exam.where(id: ids)
            mexam_all = exams.first&.exscheduledetail&.exschedule&.mexam
            exschedule = exams.first&.exscheduledetail&.exschedule
            exam_time = exschedule&.dtexam&.in_time_zone("Asia/Ho_Chi_Minh")&.strftime('%d_%m_%Y')
            subject_name = convert_vietnamese_to_ascii(mexam_all&.mmodule&.subject&.name)
            room = exams.first&.exscheduledetail&.room&.split('$$$')&.dig(1),
            zip_file_name = "#{subject_name}_#{room}_#{exam_time}"

            pdfs = []
            file_names = []

            exams.each do |exam|
                mexam = exam&.exscheduledetail&.exschedule&.mexam
                pdfs << create_exam_pdf(exam, include_correct_answers)

                student = exam.student
                student_name = student&.last_name + " " + student&.first_name
                student_code = student&.sid

                file_names << convert_vietnamese_to_ascii("#{student_code}_#{student_name}")
            end

            if pdfs.length == 1
                send_data pdfs[0], type: 'application/pdf', disposition: 'attachment', filename: "#{file_names[0]}.pdf"
            else
                zip_data = create_zip_from_pdfs(pdfs, file_names)

                send_data zip_data, type: 'application/zip', disposition: 'attachment', filename: "#{zip_file_name}.zip"
            end
        rescue
            redirect_to :back, alert: "Lỗi xuất file"
        end
    end

    # Nguyen Xuan Khoa
    # 20/09/2024
    # Tạo file zip chứa các file pdf
    def create_zip_from_pdfs(pdfs, file_names)
        zip_io = StringIO.new

        Zip::OutputStream.write_buffer(zip_io) do |zip|
            pdfs.each_with_index do |pdf, index|
            zip.put_next_entry("#{file_names[index]}.pdf")

            zip.write pdf
            end
        end

        zip_io.rewind
        zip_io.read
    end

    # Nguyen Xuan Khoa
    # 22/10/2024
    # Tạo các file pdf theo từng bài thi
    def create_exam_pdf(exam, include_correct_answers = false)
        exschedule = exam&.exscheduledetail&.exschedule
        stype_exam = exschedule&.stype_exam
        ctg_schedule = exschedule&.ctg
        mexam = exschedule&.mexam
        quesitems =
            if ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && ctg_schedule.nil?
                mexam.quesitems.where(smethod: stype_exam).includes(:qmultichoices, :qessays, :docs)
            elsif ['THI-DI', 'THI-LAI', 'THI-CAI-THIEN'].include?(stype_exam) && !ctg_schedule.nil?
                mexam.quesitems.where(smethod: stype_exam)
                            .where(ctg: ctg_schedule)
                            .includes(:qmultichoices, :qessays, :docs)
            else
                mexam.quesitems.includes(:qmultichoices, :qessays, :docs)
            end
        student = exam.student
        student_name = student&.last_name + " " + student&.first_name
        student_code = student&.sid
        start_time = exam&.dtstart
        end_time = exam&.dtend
        academic_year = exschedule&.syear.split('$$$')[1].to_i
        academic_year = "#{academic_year} - #{academic_year + 1}"
        semester =  exschedule&.syear.split('$$$')[0]
        # Lấy thứ tự câu hỏi đã random
        question_order = JSON.parse(exam.quesorders || '[]')
        options_order = JSON.parse(exam.optorders || '{}')

        # Danh sách các câu trắc nghiệm
        q_multi_choice_list = quesitems.where(quesitems: {stype: 'TRAC-NGHIEM'}).map do |question|
            files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }

            # Sắp xếp đáp án theo thứ tự đã random trong optorders
            option_order = options_order["options_order_#{question.id}"] || question.qmultichoices.pluck(:id)
            
            # Sắp xếp options theo thứ tự từ optorders
            sorted_options = question.qmultichoices.sort_by { |o| option_order.index(o.id) || Float::INFINITY }.map do |option|
                {
                    id: option.id,
                    content: option.answer,
                    is_correct: option&.is_correct == "1"
                }
            end

            {
                id: question.id,
                content: question.content,
                media: files,
                answer_id: question.excontents.find_by(exam_id: exam.id, quesitem_id: question.id)&.subquestid&.to_i,
                options: sorted_options, # Cập nhật với thứ tự đã random từ optorders
                include_correct_answers: include_correct_answers
            }
        end

        # Sắp xếp danh sách câu trắc nghiệm theo thứ tự đã random trong quesorders
        sorted_q_multi_choice_list = q_multi_choice_list.sort_by { |q| question_order.index(q[:id]) || Float::INFINITY }
        # Đếm tổng số câu đúng
        total_multichoice_ex = Excontent.joins(:quesitem)
                                        .where(exam_id: exam.id, quesitems: { stype: 'TRAC-NGHIEM' })
                                        .where.not(mark: 0)
                                        .count


        # Danh sách các câu tự luận
        q_qessay_list = quesitems.where(quesitems: {stype: 'TU-LUAN'}).map do |question|
            files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }
            {
                id: question.id,
                content: question&.content,
                media: files,
                answer_id: question&.excontents.find_by(exam_id: exam.id, quesitem_id: question.id)&.subquestid&.to_i,
                options: question&.qessays.map do |option|
                    files = option.docs.joins(:mediafile).pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }
                    excontent = Excontent.find_by(exam_id: exam.id, quesitem_id: question.id, subquestid: option.id)
                    {
                        id: option.id,
                        title: option.question,
                        answer: excontent&.answer || '',
                        media: files
                    }
                end || []
            }
        end

        # Sắp xếp danh sách câu tự luận theo thứ tự đã random trong quesorders
        sorted_q_qessay_list = q_qessay_list.sort_by { |q| question_order.index(q[:id]) || Float::INFINITY }

        render_to_string pdf: "some_file_name",
                               template: 'pdf_template/exam_student_pdf',
                               encoding: "UTF-8",
                               page_size: 'A4',
                               margin: { top: 20, bottom: 20, left: 25, right: 15 },
                               disable_smart_shrinking: true,
                               locals: {
                                    q_multi_choice_list: sorted_q_multi_choice_list,
                                    student_name: student_name,
                                    student_code: student_code,
                                    start_time: start_time,
                                    end_time: end_time,
                                    subject_name: mexam&.mmodule&.subject&.name,
                                    subject_code: mexam&.mmodule&.subject&.scode,
                                    q_qessay_list: sorted_q_qessay_list,
                                    academic_year: academic_year,
                                    semester: semester,
                                    exschedule_dtexam: exschedule&.dtexam,
                                    mexam_stype: mexam&.stype,
                                    total_ques: sorted_q_multi_choice_list.count,
                                    total_correct: total_multichoice_ex,
                                    mark: exam&.mark
                                }
    end

    def export_exam_original_docx
      unless is_access("SFTRAINING-MEXAMS-EXPORT-DOCX", "READ")
        flash[:notice] = "Bạn không có quyền cho chức năng này!"
        redirect_to exam_result_statistics_path(lang: session[:lang]) and return
      end

      mexam_id = params[:mexam_id].to_i
      mexam = Mexam.find(mexam_id)
      questmps = mexam.questmps.includes(:qmultichoices, :qessays, :docs)
    
      # Get danh sách các câu trắc nghiệm
      q_multi_choice_list = questmps.where(questmps: {stype: 'TRAC-NGHIEM'})
      q_multi_choice_list = q_multi_choice_list.map do |question|
        files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
        {
          id: question.id,
          content: question&.content,
          media: files,
          options: question&.qmultichoices.map do | option |
            {
              id: option&.id,
              content: option&.answer,
              is_correct: option&.is_correct == "1"
            }
          end || []
        }
      end
      
      # Get danh sách các câu tự luận
      q_qessay_list = questmps.where(questmps: {stype: 'TU-LUAN'})
      q_qessay_list = q_qessay_list.map do |question|
        files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
        {
          id: question.id,
          content: question&.content,
          media: files,
          options: question&.qessays.map do | option |
            files = option.docs.joins(:mediafile).pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
            {
              id: option&.id,
              title: option&.question,
              answer: option&.answer,
              media: files
            }
          end || []
        }
      end
    
      # Tạo file DOCX
      file_name = "BO_CAU_HOI_#{mexam.id}_#{Time.current.strftime('%Y%m%d%H%M%S')}.docx"
      file_path = Rails.root.join('tmp', file_name)
    
      FileUtils.mkdir_p(Rails.root.join('tmp')) unless Dir.exist?(Rails.root.join('tmp'))
    
      temp_files_to_cleanup = []
    
      begin
        Caracal::Document.save file_path do |docx|
          # Header của bộ câu hỏi
          docx.h1 "Bộ câu hỏi: #{mexam.name}", align: :center, font: 'Times New Roman', size: 36, bold: true
          docx.h2 "Loại bộ câu hỏi: #{mexam.stype}", align: :center, font: 'Times New Roman', size: 36
          docx.hr
    
          # Phần trắc nghiệm
          unless q_multi_choice_list.empty?
            docx.h2 "PHẦN I: TRẮC NGHIỆM"
            docx.p
            
            q_multi_choice_list.each_with_index do |question, index|
              temp_files = process_html_content_with_question_title(docx, question[:content], "Câu #{index + 1}: ")
              temp_files_to_cleanup.concat(temp_files)

              if question[:media] && question[:media].any?
                question[:media].each do |media_path|
                  add_image_safely(docx, media_path)
                end
              end

              options_labels = ['A', 'B', 'C', 'D']
              question[:options].each_with_index do |option, opt_index|
                label = options_labels[opt_index] || (opt_index + 1).to_s

                temp_files = process_html_content_with_option_label(docx, option[:content], label, option[:is_correct])
                temp_files_to_cleanup.concat(temp_files)
              end
              
              docx.p
            end
          end
    
          # Phần tự luận
          unless q_qessay_list.empty?
            docx.page
            docx.h2 "PHẦN II: TỰ LUẬN"
            docx.p
            
            q_qessay_list.each_with_index do |question, index|
              temp_files = process_html_content_with_question_title(docx, question[:content], "Câu #{q_multi_choice_list.length + index + 1}: ")
              temp_files_to_cleanup.concat(temp_files)

              if question[:media] && question[:media].any?
                question[:media].each do |media_path|
                  add_image_safely(docx, media_path)
                end
              end

              question[:options].each_with_index do |essay_part, part_index|
                if essay_part[:title].present?
                  temp_files = process_html_content_with_question_title(docx, essay_part[:title], "#{part_index + 1}. ")
                  temp_files_to_cleanup.concat(temp_files)
                end

                if essay_part[:media] && essay_part[:media].any?
                  essay_part[:media].each do |media_path|
                    add_image_safely(docx, media_path)
                  end
                end

                if essay_part[:answer].present?
                  temp_files = process_html_content_with_answer_label(docx, essay_part[:answer])
                  temp_files_to_cleanup.concat(temp_files)
                end
              end
              
              docx.p
            end
          end
    
          # Footer
          docx.hr
          docx.p "Bộ câu hỏi được xuất vào lúc #{Time.current.strftime('%d/%m/%Y %H:%M')}", 
                 align: :center, size: 20, color: '808080', italic: true
        end
    
      ensure
        temp_files_to_cleanup.each do |temp_file|
          cleanup_temp_file(temp_file)
        end
      end

      send_file file_path, 
                filename: file_name,
                type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                disposition: 'attachment'
    end

    # Author: Nguyen Xuan Khoa 
    # Date: 28/09/2024
    # Description: xuất pdf bộ đề gốc đã bốc
    def export_exam_original_pdf
        mexam_id = params[:mexam_id].to_i
        
        begin
            mexam = Mexam.find(mexam_id)
            subject_name = convert_vietnamese_to_ascii(mexam&.mmodule&.subject&.name)

            # Lấy danh sách tổ hợp smethod và ctg duy nhất
            smethod_ctg_combinations = mexam.quesitems.pluck(:smethod, :ctg).uniq.compact
          
            # Xử lý trường hợp không có smethod hoặc chỉ có smethod nil
            if smethod_ctg_combinations.empty?
                pdf = create_mexam_original_pdf(mexam, nil, nil)
                file_name = "Mã đề - #{mexam.id} - Thi lần 1".force_encoding('UTF-8')
                send_data pdf, type: 'application/pdf', disposition: 'attachment', filename: "#{file_name}.pdf"
                return
            end
          
            # Kiểm tra xem có quesitem nào có smethod là nil không
            has_nil_smethod = mexam.quesitems.where(smethod: nil).exists?
            if has_nil_smethod
                # Thêm các tổ hợp smethod nil với các giá trị ctg tương ứng
                nil_ctg_values = mexam.quesitems.where(smethod: nil).pluck(:ctg).uniq
                nil_ctg_values.each do |ctg|
                    smethod_ctg_combinations << [nil, ctg]
                end
            end
          
            # Nếu chỉ có một tổ hợp, xuất một file PDF duy nhất
            if smethod_ctg_combinations.length == 1
                smethod, ctg = smethod_ctg_combinations.first
                pdf = create_mexam_original_pdf(mexam, smethod, ctg)
                
                # Định dạng tên file dựa trên smethod và ctg
                file_name = format_file_name(mexam.id, smethod, ctg, 'pdf')
                send_data pdf, type: 'application/pdf', disposition: 'attachment', filename: "#{file_name}.pdf"
                return
            end
          
            # Xuất nhiều PDF dưới dạng một file zip, mã hóa UTF-8 cho tên file
            Encoding.default_internal = Encoding::UTF_8
            Encoding.default_external = Encoding::UTF_8
            Zip.unicode_names = true
          
            # Tạo file zip tạm thời
            zip_file = Tempfile.new(["Mã đề - #{mexam.id}", '.zip'])
            zip_file_path = zip_file.path
          
            # Mảng để lưu trữ tham chiếu đến các file tạm
            temp_files = []
          
            begin
                Zip::File.open(zip_file_path, Zip::File::CREATE) do |zipfile|
                    # Duyệt qua từng tổ hợp smethod và ctg
                    smethod_ctg_combinations.each do |smethod, ctg|
                        # Tạo dữ liệu PDF cho tổ hợp hiện tại
                        pdf_data = create_mexam_original_pdf(mexam, smethod, ctg)
                        
                        pdf_file = Tempfile.new(["temp-pdf", '.pdf'])
                        pdf_file.binmode
                        pdf_file.write(pdf_data)
                        pdf_file.close
                        
                        temp_files << pdf_file
                        file_name = format_file_name(mexam.id, smethod, ctg, 'pdf')
                        zipfile.add(file_name, pdf_file.path)
                    end
                end
                
                # Đọc dữ liệu file zip và gửi về client
                zip_data = File.read(zip_file_path)
                zip_filename = "Mã đề - #{mexam.id} - tất cả loại bộ đề.zip"
                send_data zip_data, type: 'application/zip', disposition: 'attachment', filename: zip_filename
            ensure
                # Xóa file tạm thời sau khi hoàn thành
                zip_file.close
                zip_file.unlink
                
                # Xóa các file PDF tạm thời
                temp_files.each do |file|
                    file.unlink if file && File.exist?(file.path)
                end
            end
        rescue => e
            redirect_to :back, alert: "Lỗi xuất file: #{e.message}"
        end
    end
      
    # Format tên file dựa trên mexam_id, smethod và ctg
    def format_file_name(mexam_id, smethod, ctg, type)
        base_name = "Mã đề - #{mexam_id}"

        if smethod.nil?
            smethod_text = "Thi lần 1"
        else
            smethod_text = format_smethod_name(smethod)
        end

        ctg_text = case ctg
                    when nil, "MAIN"
                      " - Bộ chính"
                    else
                      " - Bộ dự phòng"
                    end
        if type == 'pdf'
            "#{base_name} - #{smethod_text}#{ctg_text}.pdf".force_encoding('UTF-8')
        else
            "#{base_name} - #{smethod_text}#{ctg_text}".force_encoding('UTF-8')
        end
    end
      
    # Hàm tạo file PDF với các tham số smethod và ctg
    def create_mexam_original_pdf(mexam, smethod = nil, ctg = nil)
        quesitems = mexam.quesitems.includes(:qmultichoices, :qessays, :docs)
        
        # Lọc theo smethod nếu có
        if !smethod.nil?
          quesitems = quesitems.where(smethod: smethod)
        end
        
        # Lọc theo ctg nếu có
        if !ctg.nil?
          quesitems = quesitems.where(ctg: ctg)
        end
        
        # Danh sách các câu trắc nghiệm
        q_multi_choice_list = quesitems.where(quesitems: {stype: 'TRAC-NGHIEM'})
        q_multi_choice_list = q_multi_choice_list.map do |question|
          files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
          {
            id: question.id,
            content: question&.content,
            media: files,
            options: question&.qmultichoices.map do | option |
              {
                id: option&.id,
                content: option&.answer,
                is_correct: option&.is_correct == "1"
              }
            end || []
          }
        end
        
        # Danh sách các câu tự luận
        q_qessay_list = quesitems.where(quesitems: {stype: 'TU-LUAN'})
        q_qessay_list = q_qessay_list.map do |question|
          files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
          {
            id: question.id,
            content: question&.content,
            media: files,
            options: question&.qessays.map do | option |
              files = option.docs.joins(:mediafile).pluck('mediafiles.file_name')&.map { |file|  @ROOT_IMAGE_PATH + file }
              {
                id: option&.id,
                title: option&.question,
                answer: option&.answer,
                media: files
              }
            end || []
          }
        end

        subject_name = mexam&.mmodule&.subject&.name

        # Thêm thông tin smethod vào tiêu đề
        if smethod.nil?
          subject_name += " - Thi lần 1"
        else
          subject_name += " - " + format_smethod_name(smethod)
        end
        
        # Thêm thông tin ctg vào tiêu đề
        if ctg.nil? || ctg == "MAIN"
          subject_name += " - Bộ chính"
        else
          subject_name += " - Bộ dự phòng"
        end
        
        # Render template PDF và trả về dữ liệu
        render_to_string pdf: "some_file_name",
                               template: 'pdf_template/mexam_original_pdf',
                               encoding: "UTF-8",
                               page_size: 'A4',
                               margin: { top: 20, bottom: 20, left: 25, right: 15 },
                               disable_smart_shrinking: true,
                               locals: {
                                 q_multi_choice_list: q_multi_choice_list,
                                 subject_name: subject_name,
                                 subject_code: mexam&.mmodule&.subject&.scode,
                                 q_qessay_list: q_qessay_list,
                                 smethod: smethod,
                                 ctg: ctg
                               }
    end

    def export_exam_original_excel
        mexam_id = params[:mexam_id].to_i
        
        begin
          mexam = Mexam.find(mexam_id)
          subject_name = convert_vietnamese_to_ascii(mexam&.mmodule&.subject&.name)
      
          # Lấy danh sách tổ hợp smethod và ctg duy nhất
          smethod_ctg_combinations = mexam.quesitems.pluck(:smethod, :ctg).uniq.compact
          
          # Xử lý trường hợp không có smethod hoặc chỉ có smethod nil
          if smethod_ctg_combinations.empty?
            excel = create_mexam_original_excel(mexam, nil, nil)
            file_name = "Mã đề - #{mexam.id} - Thi lần 1".force_encoding('UTF-8')
            send_data excel.to_stream.read, type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', disposition: 'attachment', filename: "#{file_name}.xlsx"
            return
          end
          
          # Kiểm tra xem có quesitem nào có smethod là nil không
          has_nil_smethod = mexam.quesitems.where(smethod: nil).exists?
          if has_nil_smethod
            # Thêm các tổ hợp smethod nil với các giá trị ctg tương ứng
            nil_ctg_values = mexam.quesitems.where(smethod: nil).pluck(:ctg).uniq
            nil_ctg_values.each do |ctg|
              smethod_ctg_combinations << [nil, ctg]
            end
          end
          
          # Nếu chỉ có một tổ hợp, xuất một file Excel duy nhất
          if smethod_ctg_combinations.length == 1
            smethod, ctg = smethod_ctg_combinations.first
            excel = create_mexam_original_excel(mexam, smethod, ctg)
            
            # Định dạng tên file dựa trên smethod và ctg
            file_name = format_file_name(mexam.id, smethod, ctg, 'excel')
            send_data excel.to_stream.read, type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', disposition: 'attachment', filename: "#{file_name}.xlsx"
            return
          end
          
          # Xuất nhiều Excel dưới dạng một file zip, mã hóa UTF-8 cho tên file
          Encoding.default_internal = Encoding::UTF_8
          Encoding.default_external = Encoding::UTF_8
          Zip.unicode_names = true
          
          # Tạo file zip tạm thời
          zip_file = Tempfile.new(["Mã đề - #{mexam.id}", '.zip'])
          zip_file_path = zip_file.path
          
          # Mảng để lưu trữ tham chiếu đến các file tạm
          temp_files = []
          
          begin
            Zip::File.open(zip_file_path, Zip::File::CREATE) do |zipfile|
              # Duyệt qua từng tổ hợp smethod và ctg
              smethod_ctg_combinations.each do |smethod, ctg|
                # Tạo dữ liệu Excel cho tổ hợp hiện tại
                excel = create_mexam_original_excel(mexam, smethod, ctg)
                
                excel_file = Tempfile.new(["temp-excel", '.xlsx'])
                excel_file.binmode
                excel_file.write(excel.to_stream.read)
                excel_file.close
                
                temp_files << excel_file
                file_name = format_file_name(mexam.id, smethod, ctg, 'excel')
                zipfile.add("#{file_name}.xlsx", excel_file.path)
              end
            end
            
            # Đọc dữ liệu file zip và gửi về client
            zip_data = File.read(zip_file_path)
            zip_filename = "Mã đề - #{mexam.id} - tất cả loại bộ đề.zip"
            send_data zip_data, type: 'application/zip', disposition: 'attachment', filename: zip_filename
          ensure
            # Xóa file tạm thời sau khi hoàn thành
            zip_file.close
            zip_file.unlink
            
            # Xóa các file Excel tạm thời
            temp_files.each do |file|
              file.unlink if file && File.exist?(file.path)
            end
          end
        rescue => e
          redirect_to :back, alert: "Lỗi xuất file Excel: #{e.message}"
        end
    end

    def create_mexam_original_excel(mexam, smethod = nil, ctg = nil)
        quesitems = mexam.quesitems.includes(:qmultichoices, :qessays, :docs)
        
        # Lọc theo smethod nếu có
        if !smethod.nil?
            quesitems = quesitems.where(smethod: smethod)
        end
        
        # Lọc theo ctg nếu có
        if !ctg.nil?
            quesitems = quesitems.where(ctg: ctg)
        end
        
        # Danh sách các câu trắc nghiệm
        q_multi_choice_list = quesitems.where(quesitems: {stype: 'TRAC-NGHIEM'})
        q_multi_choice_list = q_multi_choice_list.map do |question|
        files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }
        {
            id: question.id,
            content: question&.content,
            media: files,
            options: question&.qmultichoices.map do |option|
            {
                id: option&.id,
                content: option&.answer,
                is_correct: option&.is_correct == "1"
            }
            end || []
        }
        end
        
        # Danh sách các câu tự luận
        q_qessay_list = quesitems.where(quesitems: {stype: 'TU-LUAN'})
        q_qessay_list = q_qessay_list.map do |question|
        files = question&.docs&.joins(:mediafile)&.pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }
        {
            id: question.id,
            content: question&.content,
            media: files,
            options: question&.qessays.map do |option|
                files = option.docs.joins(:mediafile).pluck('mediafiles.file_name')&.map { |file| @ROOT_IMAGE_PATH + file }
                {
                    id: option&.id,
                    title: option&.question,
                    answer: option&.answer,
                    media: files
                }
            end || []
        }
        end
    
        subject_name = mexam&.mmodule&.subject&.name
    
        # Thêm thông tin smethod vào tiêu đề
        if smethod.nil?
            subject_name += " - Thi lần 1"
        else
            subject_name += " - " + format_smethod_name(smethod)
        end
        
        # Thêm thông tin ctg vào tiêu đề
        if ctg.nil? || ctg == "MAIN"
            subject_name += " - Bộ chính"
        else
            subject_name += " - Bộ dự phòng"
        end
        
        # Tạo workbook và worksheet
        p = Axlsx::Package.new
        wb = p.workbook
        
        # Định dạng styles cho Excel
        styles = define_excel_styles(wb)
        
        # Tạo sheet chính
        wb.add_worksheet(name: "Bộ đề") do |sheet|
            # Thiết lập tùy chọn in trang
            sheet.page_setup do |ps|
                ps.fit_to_width = 1
                ps.fit_to_height = 999 # Thay đổi theo nhu cầu
                ps.orientation = :portrait
                ps.paper_size = 9 # A4
                ps.scale = 100
            end
            
            # Thiết lập margin
            sheet.page_margins do |margins|
                margins.left = 0.7
                margins.right = 0.7
                margins.top = 0.8
                margins.bottom = 0.8
                margins.header = 0.3
                margins.footer = 0.3
            end
            
            # Tạo header
            create_excel_header(sheet, styles, subject_name, mexam&.mmodule&.subject&.scode)
            
            # Tạo phần trắc nghiệm
            if q_multi_choice_list.length > 0
                create_multi_choice_section(sheet, styles, q_multi_choice_list)
            end
            
            # Tạo phần tự luận
            if q_qessay_list.length > 0
                create_essay_section(sheet, styles, q_qessay_list)
            end
            
            # Điều chỉnh kích thước cột
            sheet.column_widths 15, 70, 15
            
            # Thiết lập in tiêu đề trên mỗi trang
            sheet.print_options do |options|
                options.grid_lines = false
                options.horizontal_centered = true
            end
        end
        
        return p
    end
    
    # Định nghĩa các styles cho Excel
    def define_excel_styles(wb)
        {
        title: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 16,
            b: true,
            alignment: { horizontal: :center }
        ),
        header: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            alignment: { horizontal: :center }
        ),
        header_bold: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { horizontal: :center }
        ),
        section_title: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 14,
            b: true
        ),
        question_number: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { vertical: :top }
        ),
        question_content: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            alignment: { vertical: :top, wrap_text: true }
        ),
        option_letter: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { horizontal: :right, vertical: :top }
        ),
        option_content: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            alignment: { vertical: :top, wrap_text: true }
        ),
        option_correct: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { vertical: :top, wrap_text: true },
            bg_color: '90EE90',  # Màu xanh lá cây cho đáp án đúng (thay đổi từ E6F5FF)
            border: { style: :thin, color: '000000', edges: [:left, :right, :top, :bottom] }
        ),
        correct_answer_label: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { vertical: :top },
            fg_color: '008000'  # Màu xanh lá đậm cho chữ "Đáp án đúng"
        ),
        image_placeholder: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            i: true,
            alignment: { vertical: :top },
            fg_color: '666666'
        ),
        answer_label: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            b: true,
            alignment: { vertical: :top }
        ),
        answer_content: wb.styles.add_style(
            font_name: 'Times New Roman',
            sz: 12,
            alignment: { vertical: :top, wrap_text: true }
        )
        }
    end
      
    # Tạo phần header cho Excel
    def create_excel_header(sheet, styles, subject_name, subject_code)
        # Tạo header
        sheet.add_row ['', 'TRƯỜNG ĐẠI HỌC Y DƯỢC', ''], style: styles[:header]
        sheet.add_row ['', 'BUÔN MA THUỘT', ''], style: styles[:header_bold]
        sheet.add_row ['', 'HỘI ĐỒNG THI KẾT THÚC HỌC PHẦN', ''], style: styles[:header]
        sheet.add_row ['', '', '']
        
        # Tiêu đề bộ đề
        sheet.add_row ['', "Bộ đề: #{subject_name} (#{subject_code})", ''], style: styles[:title]
        sheet.add_row ['', '', '']
        sheet.add_row ['', '', '']
    end

    # Tạo phần trắc nghiệm
    def create_multi_choice_section(sheet, styles, q_multi_choice_list)
        sheet.add_row ['', 'Phần I: Trắc nghiệm', ''], style: styles[:section_title]
        sheet.add_row ['', '', '']
        
        q_multi_choice_list.each_with_index do |question, index|
        # Thêm câu hỏi
        sheet.add_row ["Câu #{index + 1}:", strip_html_tags(question[:content]), ''], style: [styles[:question_number], styles[:question_content], nil]
        
        # Thêm thông báo về hình ảnh nếu có
        if question[:media] && question[:media].any?
            sheet.add_row ['', '[Có hình ảnh minh họa]', ''], style: [nil, styles[:image_placeholder], nil]
        end
        
        # Thêm các lựa chọn
        question[:options].each_with_index do |option, i|
            option_letter = "#{(i + 1 + 96).chr.upcase}:"
            
            # Định dạng cho từng đáp án
            if option[:is_correct]
                # Đáp án đúng - thêm "(Đáp án đúng)" ở cột C (phải)
                sheet.add_row [option_letter, strip_html_tags(option[:content]), '(Đáp án đúng)'], 
                             style: [styles[:option_letter], styles[:option_correct], styles[:correct_answer_label]]
            else
                sheet.add_row [option_letter, strip_html_tags(option[:content]), ''], 
                             style: [styles[:option_letter], styles[:option_content], nil]
            end
        end
        
        # Thêm dòng trống giữa các câu hỏi
        sheet.add_row ['', '', '']
        end
    end

    # Tạo phần tự luận
    def create_essay_section(sheet, styles, q_qessay_list)
        sheet.merge_cells "B#{sheet.rows.count+1}:C#{sheet.rows.count+1}"
        sheet.add_row ['', 'Phần II: Tự luận', ''], style: styles[:section_title]
        sheet.add_row ['', '', '']
        
        q_qessay_list.each_with_index do |question, index|
        # Thêm câu hỏi
        sheet.add_row ["Câu #{index + 1}:", strip_html_tags(question[:content]), ''], style: [styles[:question_number], styles[:question_content], nil]
        
        # Thêm thông báo về hình ảnh nếu có
        if question[:media] && question[:media].any?
            sheet.add_row ['', '[Có hình ảnh minh họa]', ''], style: [nil, styles[:image_placeholder], nil]
        end
        
        # Thêm các ý trong câu hỏi
        question[:options].each_with_index do |option, i|
            # Tiêu đề của ý
            sheet.add_row ["", strip_html_tags(option[:title]), ''], style: [nil, styles[:question_content], nil]
            
            # Thông báo về hình ảnh nếu có
            if option[:media] && option[:media].any?
            sheet.add_row ['', '[Có hình ảnh minh họa]', ''], style: [nil, styles[:image_placeholder], nil]
            end
            
            # Đáp án
            sheet.add_row ["", "Đáp án:", ''], style: [nil, styles[:answer_label], nil]
            sheet.add_row ["", strip_html_tags(option[:answer]), ''], style: [nil, styles[:answer_content], nil]
            
            # Thêm dòng trống giữa các ý
            sheet.add_row ['', '', '']
        end
        
        # Thêm dòng trống giữa các câu hỏi
        sheet.add_row ['', '', '']
        end
    end

    # Hàm bỏ các thẻ HTML trong nội dung
    def strip_html_tags(html)
        return '' if html.nil?
        ActionController::Base.helpers.strip_tags(html)
    end

    private

    # Nguyen Xuan Khoa
    # 28/09/2024
    def convert_vietnamese_to_ascii(input)
        vietnamese_chars = {
          'á' => 'a', 'à' => 'a', 'ả' => 'a', 'ã' => 'a', 'ạ' => 'a',
          'ấ' => 'a', 'ầ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a', 'ậ' => 'a',
          'ắ' => 'a', 'ằ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a', 'ặ' => 'a',
          'ó' => 'o', 'ò' => 'o', 'ỏ' => 'o', 'õ' => 'o', 'ọ' => 'o',
          'ố' => 'o', 'ồ' => 'o', 'ổ' => 'o', 'ỗ' => 'o', 'ộ' => 'o',
          'ớ' => 'o', 'ờ' => 'o', 'ở' => 'o', 'ỡ' => 'o', 'ợ' => 'o',
          'ú' => 'u', 'ù' => 'u', 'ủ' => 'u', 'ũ' => 'u', 'ụ' => 'u',
          'ứ' => 'u', 'ừ' => 'u', 'ử' => 'u', 'ữ' => 'u', 'ự' => 'u',
          'í' => 'i', 'ì' => 'i', 'ỉ' => 'i', 'ĩ' => 'i', 'ị' => 'i',
          'ế' => 'e', 'ề' => 'e', 'ể' => 'e', 'ễ' => 'e', 'ệ' => 'e',
          'ý' => 'y', 'ỳ' => 'y', 'ỷ' => 'y', 'ỹ' => 'y', 'ỵ' => 'y',
          'đ' => 'd', 'ư' => 'u'
        }

        normalized = input.downcase.tr(" ", "_").gsub(/[#{vietnamese_chars.keys.join}]/) { |char| vietnamese_chars[char] }
        normalized
    end

    def format_smethod_name(smethod)
        case smethod
        when "THI-DI"
            return "Thi đi"
        when "THI-LAI"
            return "Thi lại"
        when "THI-CAI-THIEN"
            return "Thi cải thiện"
        else
            return smethod
        end
    end
end